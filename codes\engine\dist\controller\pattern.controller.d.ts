import { GetPatternDto } from 'src/dto/get-pattern.dto';
import { Pattern } from 'src/interface/pattern.interface';
import { PatternService } from 'src/services/pattern.service';
import { Logger } from 'winston';
export declare class PatternController {
    private readonly appService;
    private readonly logger;
    constructor(appService: PatternService, logger: Logger);
    getPattern(body: GetPatternDto): Promise<Pattern[]>;
}
