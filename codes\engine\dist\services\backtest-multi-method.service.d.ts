import { Logger } from 'winston';
import { BacktestService } from './backtest.service';
import { BacktestMultiPerformance } from 'src/interface/backtest-multi-performance.interface';
import { MethodResult } from 'src/interface/method-result.interface';
import { GetBacktestMultiMethodDto } from 'src/dto/get-backtest-multi-method.dto';
import { MethodService } from './method.service';
export declare class BacktestMultiMethodService {
    private readonly logger;
    private readonly backtestService;
    private readonly methodService;
    constructor(logger: Logger, backtestService: BacktestService, methodService: MethodService);
    getBacktestMultiMethodResult(param: GetBacktestMultiMethodDto): Promise<MethodResult[]>;
    getBacktestMultiMethodPerformance(param: GetBacktestMultiMethodDto): Promise<BacktestMultiPerformance>;
    getBacktestMutilMethodBoth(param: GetBacktestMultiMethodDto): Promise<{
        result: MethodResult[];
        performance: BacktestMultiPerformance;
    }>;
}
