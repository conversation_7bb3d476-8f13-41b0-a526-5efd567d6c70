import { Inject, Injectable, OnModuleInit } from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import { logDetail } from 'src/util/log-detail.util';
import configurations from 'src/configurations';
import { AdapterService } from './adapter.service';
import { toMiliseconds } from 'src/util/to-milliseconds.util';
import { HistoricalCacheService } from './historical-cache.service';
import { HistoricalService } from './historical.service';
import { InstrumentService } from './instrument.service';
import { shuffleArray } from 'src/util/suffle-array.util';
import { getSymbolsSlice } from 'src/util/get-symbols-slice.util';
import { MethodStatusService } from './method-status.service';

@Injectable()
export class HistoricalIngestionService {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER)
    private readonly logger: Logger,
    private readonly historicalService: HistoricalService,
    private readonly methodStatusService: MethodStatusService,
    private readonly adapterService: AdapterService,
    private readonly historicalCacheService: HistoricalCacheService,
    private readonly instrumentService: InstrumentService,
  ) { }

  async ingestHistorical(interval?: string) {
    const engineMode = configurations('ENGINE_MODE');
    const disableCluster = configurations('DISABLE_CLUSTER');
    const intervals = interval ? [interval] : configurations('INTERVALS');
    const executionIntervals = configurations('EXECUTION_INTERVAL');

    // Worker mode
    if (engineMode === 'worker') {
      let symbols = await this.instrumentService.getSymbols();
      symbols = disableCluster === 'false'
        ? shuffleArray(getSymbolsSlice(symbols))
        : shuffleArray(symbols);

      await this.ingestSymbols(symbols, () => intervals);
    }

    // Service mode
    if (engineMode === 'service') {
      const methodStatuses = await this.methodStatusService.getAllMethodStatus();
      let symbols = [...new Set(methodStatuses.map((item) => item.symbol))];

      symbols = disableCluster === 'false'
        ? shuffleArray(getSymbolsSlice(symbols))
        : shuffleArray(symbols);
      const intervals = interval ? [interval] : [...new Set(methodStatuses.map((item) => item.interval)), executionIntervals];
      await this.ingestSymbols(
        symbols,
        () => intervals,
      );
    }
  }

  private async ingestSymbols(
    symbols: string[],
    getIntervals: (symbol: string) => string[]
  ) {
    for (const symbol of symbols) {
      for (const interval of getIntervals(symbol)) {
        this.logger.info(`Ingesting ${symbol} ${interval}`);
        await this.process(
          symbol,
          interval
        );
      }
    }
  };

  private async process(symbol: string, interval: string) {
    try {
      const startDate = new Date(0)
      const endDate = new Date();
      const defaultLimit = configurations('DEFAULT_LIMIT');
      const startTime = startDate.getTime();
      const endTime = endDate.getTime();
      const timeRange = toMiliseconds(interval);
      const instrument = await this.instrumentService.getInstrument({
        symbol,
      });

      if (!instrument) return;

      const listedTime = new Date(Number(instrument[0].listedTime)).getTime()
      const rowLengthTarget = Math.ceil(
        (endTime - listedTime) / toMiliseconds(interval),
      );

      for (let i = endTime; i > startTime; i -= timeRange * defaultLimit) {
        const startScan = new Date(i - timeRange * defaultLimit);
        const endScan = new Date(i);
        const existHistoricalCacheCount =
          await this.historicalCacheService.countHistorical(
            symbol,
            interval,
            startScan,
            endScan,
          );
        const existHistoricalCount =
          await this.historicalService.countHistorical(
            symbol,
            interval,
            startScan,
            endScan,
          );
        if (
          existHistoricalCacheCount === defaultLimit &&
          existHistoricalCount === defaultLimit
        ) {
          const existHistoricalCacheFullCount =
            await this.historicalCacheService.countHistorical(
              symbol,
              interval,
            );
          const existHistoricalFullCount =
            await this.historicalService.countHistorical(
              symbol,
              interval,
            );

          if (
            existHistoricalCacheFullCount === existHistoricalFullCount &&
            existHistoricalCacheFullCount >= rowLengthTarget
          ) {
            break;
          }
          continue;
        }
        if (
          existHistoricalCacheCount === defaultLimit &&
          existHistoricalCount !== defaultLimit
        ) {
          const existHistoricalCache =
            await this.historicalCacheService.getHistorical({
              symbol,
              interval,
              sort: 'DESC',
              limit: defaultLimit,
              start: startScan,
              end: endScan,
            });
          await this.historicalService.insertHistorical(existHistoricalCache);
          continue;
        }
        if (
          existHistoricalCacheCount !== defaultLimit &&
          existHistoricalCount === defaultLimit
        ) {
          const existHistorical = await this.historicalService.getHistorical({
            symbol,
            interval,
            sort: 'DESC',
            limit: defaultLimit,
            start: startScan,
            end: endScan,
          });
          await this.historicalCacheService.insertHistorical(existHistorical);
          continue;
        }

        const newHistorical = await this.adapterService.fetchBybitHistorical({
          symbol,
          interval,
          sort: 'DESC',
          limit: defaultLimit,
          start: startScan,
          end: endScan,
        });

        await this.historicalService.insertHistorical(newHistorical);
        await this.historicalCacheService.insertHistorical(newHistorical);

        if (
          existHistoricalCount === newHistorical.length &&
          existHistoricalCacheCount === newHistorical.length
        ) {
          break;
        }
      }
      return;
    } catch (error) {
      this.logger.error(
        'Failed to process historical data',
        logDetail({
          class: 'AppService',
          function: 'process',
          param: { symbol, interval },
          error,
        }),
      );
      throw new Error('Failed to process historical data');
    }
  }
}
