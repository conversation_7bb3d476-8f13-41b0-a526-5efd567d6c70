import { Inject, Injectable } from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import { logDetail } from 'src/util/log-detail.util';
import { GetStaticPlanDto } from 'src/dto/get-static-plan.dto';
import { Plan } from 'src/interface/plan.interface';
import { InstrumentService } from './instrument.service';
import { getEntry } from 'src/util/get-entry-price.util';
import { getTargetPrice } from 'src/util/get-target-price.util';
import { getExpiryDate } from 'src/util/get-expiry-date.util';
import { Pattern } from 'src/interface/pattern.interface';
import { Historical } from 'src/interface/historical.interface';
import { PatternService } from './pattern.service';

import { Instrument } from 'src/interface/instrument.interface';
import { convertToUUID } from 'src/util/convert-to-uuid.util';
import { HistoricalCacheService } from './historical-cache.service';

@Injectable()
export class StaticPlanService {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    private readonly instrumentService: InstrumentService,
    private readonly historicalCacheService: HistoricalCacheService,
    private readonly patternService: PatternService,
  ) {}

  async getPlan(
    param: GetStaticPlanDto,
    historical?: Historical[],
    patterns?: Pattern[],
    instrument?: Instrument[],
  ): Promise<Plan[]> {
    try {
      const historicalData =
        historical ?? (await this.historicalCacheService.getHistorical(param));

      const patternData =
        patterns ??
        (await this.patternService.getPattern(param, historicalData));

      param.entryPercentByClose =
        param.orderType === 'long'
          ? -Math.abs(param.entryPercentByClose)
          : Math.abs(param.entryPercentByClose);

      const diffFactor = 0.01;
      param.riskPercent =
        param.orderType === 'long'
          ? -Math.abs(param.riskPercent) - diffFactor
          : Math.abs(param.riskPercent) + diffFactor;

      param.rewardPercent =
        param.orderType === 'long'
          ? Math.abs(param.rewardPercent)
          : -Math.abs(param.rewardPercent);

      const planData: Plan[] = [];
      for (const item of patternData) {
        const id = convertToUUID(
          `${item.symbol}-${item.interval}-${item.patternType}-${item.pattern}-${item.date}`,
        );

        const instrumentData =
          instrument ??
          (await this.instrumentService.getInstrument({
            symbol: item.symbol,
          }));

        if (!instrumentData.length) return [];
        const tickSize = instrumentData[0].priceFilter.tickSize;

        const entry = getEntry({
          orderType: param.orderType,
          closePrice: item.close,
          entryPercentByClose: param.entryPercentByClose,
          tickSize,
        });
        const stopLoss = getTargetPrice({
          mathRound: param.orderType === 'long' ? 'floor' : 'ceil',
          entry,
          targetPercent: param.riskPercent,
          tickSize,
        });
        const takeProfit = getTargetPrice({
          mathRound: param.orderType === 'long' ? 'floor' : 'ceil',
          entry,
          targetPercent: param.rewardPercent,
          tickSize,
        });
        const expiryDate = getExpiryDate({
          date: item.date,
          interval: item.interval,
          validityPeriod: param.validityPeriod,
        });
        planData.push({
          id,
          methodId: param.methodId ?? '',
          orderType: param.orderType,
          entry,
          stopLoss,
          takeProfit,
          expiryDate,
          entryPercentByClose: param.entryPercentByClose,
          riskPercent: param.riskPercent,
          rewardPercent: param.rewardPercent,
          validityPeriod: param.validityPeriod,
          stopPercent: ((stopLoss - entry) / entry) * 100,
          profitPercent: ((takeProfit - entry) / entry) * 100,
          date: item.date,
          interval: item.interval,
          symbol: item.symbol,
          patternType: item.patternType,
          pattern: item.pattern,
        });
      }

      return planData;
    } catch (error) {
      this.logger.error(
        'Failed to fetch pattern data',
        logDetail({
          class: 'AppService',
          function: 'getPlan',
          param,
          error,
        }),
      );
      throw new Error('Failed to fetch pattern data');
    }
  }
}
