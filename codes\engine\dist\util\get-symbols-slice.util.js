"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSymbolsSlice = getSymbolsSlice;
const configurations_1 = __importDefault(require("../configurations"));
function getSymbolsSlice(symbols) {
    const totalCore = (0, configurations_1.default)('CPU_ALLOCATION') ?? (0, configurations_1.default)('TOTAL_CORE');
    const coreNumber = (0, configurations_1.default)('CORE_NUMBER');
    const totalServer = (0, configurations_1.default)('TOTAL_SERVER');
    const serverNumber = (0, configurations_1.default)('SERVER_NUMBER');
    const totalSymbolPerServer = symbols.length / totalServer;
    const totalSymbolPerCore = totalSymbolPerServer / totalCore;
    const startSymbol = Math.floor(serverNumber * totalSymbolPerCore + coreNumber * totalSymbolPerCore);
    const endSymbol = Math.ceil(serverNumber * totalSymbolPerCore +
        (coreNumber + 1) * totalSymbolPerCore);
    return symbols.slice(startSymbol, endSymbol);
}
;
//# sourceMappingURL=get-symbols-slice.util.js.map