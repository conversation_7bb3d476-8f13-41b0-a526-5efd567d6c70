import { GetBacktestMethodDto } from 'src/dto/get-backtest-method.dto';
import { MethodResult } from 'src/interface/method-result.interface';
import { BacktestPerformance } from 'src/interface/backtest-performance.interface';
import { BacktestMethodService } from 'src/services/backtest-method.service';
import { Logger } from 'winston';
export declare class BacktestMethodController {
    private readonly backtestMethodService;
    private readonly logger;
    constructor(backtestMethodService: BacktestMethodService, logger: Logger);
    getResult(body: GetBacktestMethodDto): Promise<MethodResult[]>;
    getPerformance(body: GetBacktestMethodDto): Promise<BacktestPerformance>;
    generateBoth(body: GetBacktestMethodDto): Promise<{
        result: MethodResult[];
        performance: BacktestPerformance;
    }>;
}
