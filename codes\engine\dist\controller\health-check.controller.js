"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealthCheckController = void 0;
const common_1 = require("@nestjs/common");
;
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const log_detail_util_1 = require("../util/log-detail.util");
const health_check_service_1 = require("../services/health-check.service");
let HealthCheckController = class HealthCheckController {
    healthCheckService;
    logger;
    constructor(healthCheckService, logger) {
        this.healthCheckService = healthCheckService;
        this.logger = logger;
    }
    async check() {
        const { timestamp, results } = await this.healthCheckService.check();
        const hasUnreachable = Object.values(results).includes('unreachable');
        if (hasUnreachable) {
            this.logger.warn('Some services are unreachable', (0, log_detail_util_1.logDetail)({
                class: 'HealthCheckController',
                function: 'check',
                results,
            }));
            throw new common_1.HttpException({
                status: 'error',
                message: 'Some services are unreachable',
                timestamp,
                results,
            }, common_1.HttpStatus.SERVICE_UNAVAILABLE);
        }
        return {
            status: 'ok',
            timestamp,
            results,
        };
    }
};
exports.HealthCheckController = HealthCheckController;
__decorate([
    (0, common_1.Get)('/ready'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], HealthCheckController.prototype, "check", null);
exports.HealthCheckController = HealthCheckController = __decorate([
    (0, common_1.Controller)('health'),
    __param(1, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [health_check_service_1.HealthCheckService,
        winston_1.Logger])
], HealthCheckController);
//# sourceMappingURL=health-check.controller.js.map