"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var TaskService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaskService = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const configurations_1 = __importDefault(require("../configurations"));
const historical_ingestion_service_1 = require("./historical-ingestion.service");
const instrument_ingestion_service_1 = require("./instrument-ingestion.service");
const method_status_service_1 = require("./method-status.service");
const method_updater_service_1 = require("./method-updater.service");
const method_ingestion_service_1 = require("./method-ingestion.service");
const instrument_service_1 = require("./instrument.service");
const suffle_array_util_1 = require("../util/suffle-array.util");
const get_symbols_slice_util_1 = require("../util/get-symbols-slice.util");
let TaskService = TaskService_1 = class TaskService {
    historicalIngestionService;
    methodStatusService;
    instrumentIngestionService;
    methodUpdaterService;
    methodIngestionService;
    instrumentService;
    logger = new common_1.Logger(TaskService_1.name);
    isInitiated = false;
    constructor(historicalIngestionService, methodStatusService, instrumentIngestionService, methodUpdaterService, methodIngestionService, instrumentService) {
        this.historicalIngestionService = historicalIngestionService;
        this.methodStatusService = methodStatusService;
        this.instrumentIngestionService = instrumentIngestionService;
        this.methodUpdaterService = methodUpdaterService;
        this.methodIngestionService = methodIngestionService;
        this.instrumentService = instrumentService;
    }
    async onModuleInit() {
        const engineMode = (0, configurations_1.default)('ENGINE_MODE');
        if (engineMode === 'worker') {
            await this.instrumentIngestionService.ingest();
            await this.historicalIngestionService.ingestHistorical();
            this.isInitiated = true;
            const disableCluster = (0, configurations_1.default)('DISABLE_CLUSTER');
            let symbols = await this.instrumentService.getSymbols();
            symbols = disableCluster === 'false'
                ? (0, suffle_array_util_1.shuffleArray)((0, get_symbols_slice_util_1.getSymbolsSlice)(symbols))
                : (0, suffle_array_util_1.shuffleArray)(symbols);
            this.logger.log('Method ingestion loop triggered.');
            this.methodIngestionService.ingestForever(symbols);
        }
        if (engineMode === 'service') {
            (async () => {
                await this.instrumentIngestionService.ingest();
                await this.historicalIngestionService.ingestHistorical();
                this.isInitiated = true;
            })();
        }
    }
    async handleInstrumentStatus() {
        if (!this.isInitiated)
            return;
        this.logger.log('Instrument ingestion job triggered.');
        await this.instrumentIngestionService.ingest();
        this.logger.log('Historical ingestion job triggered.');
        await this.historicalIngestionService.ingestHistorical();
    }
    async handleInterval() {
        if (!this.isInitiated)
            return;
        const now = new Date();
        const minutes = now.getMinutes();
        const hours = now.getHours();
        const dayOfMonth = now.getDate();
        const dayOfWeek = now.getDay();
        const intervals = (0, configurations_1.default)('INTERVALS');
        const engineMode = (0, configurations_1.default)('ENGINE_MODE');
        for (const interval of intervals) {
            try {
                switch (interval) {
                    case 'M':
                        if (minutes === 0 && hours === 0 && dayOfMonth === 1) {
                            if (engineMode === 'worker')
                                break;
                            this.logger.log('Monthly job triggered.');
                            this.logger.log('Historical ingestion job triggered.');
                            await this.historicalIngestionService.ingestHistorical(interval);
                            this.logger.log('Method update job triggered.');
                            await this.methodStatusService.resetMethodStatus(interval);
                            await this.methodUpdaterService.updateMethod();
                        }
                        break;
                    case 'W':
                        if (minutes === 0 && hours === 0 && dayOfWeek === 1) {
                            this.logger.log('Weekly job triggered.');
                            if (engineMode === 'worker') {
                                this.logger.log('Historical ingestion job triggered.');
                                await this.historicalIngestionService.ingestHistorical();
                                break;
                            }
                            await this.historicalIngestionService.ingestHistorical(interval);
                            await this.methodStatusService.resetMethodStatus(interval);
                            await this.methodUpdaterService.updateMethod();
                        }
                        break;
                    case 'D':
                        if (minutes === 0 && hours === 0) {
                            if (engineMode === 'worker')
                                break;
                            this.logger.log('Daily job triggered.');
                            await this.historicalIngestionService.ingestHistorical(interval);
                            await this.methodStatusService.resetMethodStatus(interval);
                            await this.methodUpdaterService.updateMethod();
                        }
                        break;
                    default:
                        const intervalMinutes = parseInt(interval);
                        if (isNaN(intervalMinutes)) {
                            this.logger.warn(`Invalid interval: ${interval}`);
                            continue;
                        }
                        if (intervalMinutes >= 60 &&
                            minutes === 0 &&
                            hours % (intervalMinutes / 60) === 0) {
                            if (engineMode === 'worker')
                                break;
                            this.logger.log(`Interval ${intervalMinutes / 60}h reset triggered.`);
                            await this.historicalIngestionService.ingestHistorical(interval);
                            await this.methodStatusService.resetMethodStatus(interval);
                            await this.methodUpdaterService.updateMethod();
                        }
                        else if (intervalMinutes < 60 &&
                            minutes % intervalMinutes === 0) {
                            if (engineMode === 'worker')
                                break;
                            this.logger.log(`Interval ${interval}m job triggered.`);
                            await this.historicalIngestionService.ingestHistorical(interval);
                            await this.methodStatusService.resetMethodStatus(interval);
                            await this.methodUpdaterService.updateMethod();
                        }
                        break;
                }
            }
            catch (err) {
                this.logger.error(`Error during reset for interval ${interval}:`, err);
            }
        }
    }
};
exports.TaskService = TaskService;
__decorate([
    (0, schedule_1.Cron)('0 */4 * * *'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TaskService.prototype, "handleInstrumentStatus", null);
__decorate([
    (0, schedule_1.Cron)('* * * * *'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TaskService.prototype, "handleInterval", null);
exports.TaskService = TaskService = TaskService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [historical_ingestion_service_1.HistoricalIngestionService,
        method_status_service_1.MethodStatusService,
        instrument_ingestion_service_1.InstrumentIngestionService,
        method_updater_service_1.MethodUpdaterService,
        method_ingestion_service_1.MethodIngestionService,
        instrument_service_1.InstrumentService])
], TaskService);
//# sourceMappingURL=task.service.js.map