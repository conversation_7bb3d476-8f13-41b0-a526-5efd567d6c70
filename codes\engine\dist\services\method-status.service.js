"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MethodStatusService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const nest_winston_1 = require("nest-winston");
const method_status_entity_1 = require("../entity/method-status.entity");
const log_detail_util_1 = require("../util/log-detail.util");
const typeorm_2 = require("typeorm");
const winston_1 = require("winston");
let MethodStatusService = class MethodStatusService {
    logger;
    MethodsStatusRepository;
    constructor(logger, MethodsStatusRepository) {
        this.logger = logger;
        this.MethodsStatusRepository = MethodsStatusRepository;
    }
    async getMethodStatusByMethodId(methodId) {
        try {
            const queryBuilder = this.MethodsStatusRepository.createQueryBuilder('method-status');
            return await queryBuilder
                .where('method-status.methodId = :methodId', { methodId })
                .getOne();
        }
        catch (err) {
            this.logger.error('Failed to read method data', (0, log_detail_util_1.logDetail)({
                class: 'MethodStatusService',
                function: 'getMethodStatusByMethodId',
                error: err,
            }));
            throw new Error(`Failed to read method data`);
        }
    }
    async getMethodStatus() {
        try {
            const symbolResult = await this.MethodsStatusRepository.createQueryBuilder('method-status')
                .select('method-status.symbol')
                .where('method-status.isUpdated = :isUpdated', { isUpdated: false })
                .groupBy('method-status.symbol')
                .orderBy('RANDOM()')
                .limit(1)
                .getRawOne();
            if (!symbolResult) {
                return null;
            }
            const selectedSymbol = symbolResult['method-status_symbol'];
            const results = await this.MethodsStatusRepository.createQueryBuilder('method-status')
                .where('method-status.isUpdated = :isUpdated', { isUpdated: false })
                .andWhere('method-status.symbol = :symbol', { symbol: selectedSymbol })
                .getMany();
            return results;
        }
        catch (err) {
            this.logger.error('Failed to read method data', (0, log_detail_util_1.logDetail)({
                class: 'MethodStatusService',
                function: 'getMethodStatus',
                error: err,
            }));
            throw new Error(`Failed to read method data`);
        }
    }
    async getAllMethodStatus() {
        try {
            return await this.MethodsStatusRepository.find();
        }
        catch (err) {
            this.logger.error('Failed to read method data', (0, log_detail_util_1.logDetail)({
                class: 'MethodStatusService',
                function: 'getAllMethodStatus',
                error: err,
            }));
            throw new Error(`Failed to read method data`);
        }
    }
    async insertMethodStatus(methodId, symbol, interval) {
        try {
            const newRecords = [
                {
                    methodId,
                    interval,
                    symbol,
                    updatedAt: new Date(),
                    isUpdated: false,
                },
            ];
            await this.MethodsStatusRepository.createQueryBuilder()
                .insert()
                .into(method_status_entity_1.MethodStatusEntity)
                .values(newRecords)
                .orIgnore()
                .execute();
            return;
        }
        catch (err) {
            this.logger.error('Failed to sync method status data', (0, log_detail_util_1.logDetail)({
                class: 'MethodStatusService',
                function: 'insertMethodStatus',
                error: err,
            }));
            throw new Error('Failed to sync method status data');
        }
    }
    async updateMethodStatus(param) {
        try {
            return await this.MethodsStatusRepository.update({ methodId: param.methodId }, { updatedAt: new Date(), isUpdated: true });
        }
        catch (err) {
            this.logger.error('Failed to update method data', (0, log_detail_util_1.logDetail)({
                class: 'MethodStatusService',
                function: 'updateMethod',
                error: err,
                param,
            }));
            throw new Error(`Failed to update method data`);
        }
    }
    async resetMethodStatus(interval) {
        try {
            return await this.MethodsStatusRepository.update({ isUpdated: true, interval }, { updatedAt: new Date(), isUpdated: false });
        }
        catch (err) {
            this.logger.error('Failed to reset method data', (0, log_detail_util_1.logDetail)({
                class: 'MethodStatusService',
                function: 'resetMethod',
                error: err,
            }));
            throw new Error(`Failed to reset method data`);
        }
    }
    async deleteMethodStatus(methodId) {
        try {
            await this.MethodsStatusRepository.delete({ methodId });
        }
        catch (err) {
            this.logger.error('Failed to delete method data', (0, log_detail_util_1.logDetail)({
                class: 'MethodStatusService',
                function: 'deleteMethod',
                error: err,
            }));
            throw new Error(`Failed to delete method data`);
        }
    }
};
exports.MethodStatusService = MethodStatusService;
exports.MethodStatusService = MethodStatusService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __param(1, (0, typeorm_1.InjectRepository)(method_status_entity_1.MethodStatusEntity)),
    __metadata("design:paramtypes", [winston_1.Logger,
        typeorm_2.Repository])
], MethodStatusService);
//# sourceMappingURL=method-status.service.js.map