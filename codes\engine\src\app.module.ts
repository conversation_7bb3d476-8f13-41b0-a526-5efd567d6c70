import { Module } from '@nestjs/common';
import { WinstonModule } from 'nest-winston';
import * as winston from 'winston';
import { utilities as nestWinstonModuleUtilities } from 'nest-winston';
import * as path from 'path';
import configurations from './configurations';
import { Pattern<PERSON>ontroller } from './controller/pattern.controller';
import { CandlestickService } from './services/candlestick.service';
import { CandlestickPatternService } from './services/candlestick-pattern.service';
import { PatternService } from './services/pattern.service';
import { HealthCheckController } from './controller/health-check.controller';
import { HealthCheckService } from './services/health-check.service';
import { BacktestController } from './controller/backtest.controller';
import { StaticPlanController } from './controller/static-plan.controller';
import { BacktestService } from './services/backtest.service';
import { StaticPlanService } from './services/static-plan.service';
import { HistoricalService } from './services/historical.service';
import { OptimizeStaticPlanController } from './controller/optimize-static-plan.controller';
import { OptimizeStaticPlanService } from './services/optimize-static-plan.service';
import { DynamicPlanController } from './controller/dynamic-plan.controller';
import { DynamicPlanService } from './services/dynamic-plan.service';
import { InstrumentService } from './services/instrument.service';
import { BacktestMethodController } from './controller/backtest-method.controller';
import { BacktestMethodService } from './services/backtest-method.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HistoricalEntity } from './entity/historical.entity';
import { InstrumentEntity } from './entity/instrument.entity';
import { HistoricalController } from './controller/historical.controller';
import { InstrumentController } from './controller/instrument.controller';
import { AdapterController } from './controller/adapter.controller';
import { AdapterService } from './services/adapter.service';
import { MethodParamEntity } from './entity/method-param.entity';
import { MethodResultEntity } from './entity/method-result.entity';
import { MethodPerformanceEntity } from './entity/method-performance.entity';
import { ScheduleModule } from '@nestjs/schedule';
import { TaskService } from './services/task.service';
import { HistoricalIngestionService } from './services/historical-ingestion.service';
import { MethodService } from './services/method.service';
import { MethodController } from './controller/method.controller';
import { MethodIngestionService } from './services/method-ingestion.service';
import { MethodStatusService } from './services/method-status.service';
import { MethodUpdaterService } from './services/method-updater.service';
import { MethodStatusEntity } from './entity/method-status.entity';
import { InstrumentIngestionService } from './services/instrument-ingestion.service';
import { HistoricalCacheService } from './services/historical-cache.service';
import { BacktestMultiMethodController } from './controller/backtest-multi-method.controller';
import { BacktestMultiMethodService } from './services/backtest-multi-method.service';

const isProd = configurations('NODE_ENV') === 'production';
const engineMode = configurations('ENGINE_MODE');

let moduleConfig = {};
switch (engineMode) {
  case 'service':
    moduleConfig = {
      controllers: [
        AdapterController,
        BacktestMethodController,
        BacktestMultiMethodController,
        BacktestController,
        DynamicPlanController,
        HealthCheckController,
        InstrumentController,
        HistoricalController,
        OptimizeStaticPlanController,
        PatternController,
        StaticPlanController,
        MethodController,

      ],
      providers: [
        MethodService,
        AdapterService,
        BacktestMethodService,
        BacktestMultiMethodService,
        BacktestService,
        CandlestickPatternService,
        CandlestickService,
        DynamicPlanService,
        HealthCheckService,
        HistoricalService,
        InstrumentService,
        OptimizeStaticPlanService,
        PatternService,
        StaticPlanService,
        InstrumentIngestionService,
        HistoricalIngestionService,
        HistoricalCacheService,
        MethodStatusService,
        MethodUpdaterService,
        MethodIngestionService,
        TaskService,

      ],
    };
    break;
  case 'worker':
    moduleConfig = {
      controllers: [],
      providers: [
        MethodService,
        AdapterService,
        BacktestMethodService,
        BacktestService,
        CandlestickPatternService,
        CandlestickService,
        DynamicPlanService,
        HealthCheckService,
        HistoricalService,
        InstrumentService,
        OptimizeStaticPlanService,
        PatternService,
        StaticPlanService,
        InstrumentIngestionService,
        HistoricalIngestionService,
        HistoricalCacheService,
        MethodStatusService,
        MethodUpdaterService,
        MethodIngestionService,
        TaskService,
      ],
    };
    break;
  default:
    throw new Error(`Invalid engine mode: ${engineMode}`);
}

@Module({
  imports: [
    TypeOrmModule.forRoot(configurations('DATABASE')),
    TypeOrmModule.forFeature([
      HistoricalEntity,
      InstrumentEntity,
      MethodParamEntity,
      MethodResultEntity,
      MethodPerformanceEntity,
      MethodStatusEntity,
    ]),
    ScheduleModule.forRoot(),
    WinstonModule.forRoot({
      transports: [
        ...(isProd
          ? [
            new winston.transports.File({
              filename: path.join(configurations('LOG_DIR'), 'app.log'),
              level: configurations('LOG_LEVEL') || 'warn',
              maxsize: 10 * 1024 * 1024,
              maxFiles: 5,
              format: winston.format.combine(
                winston.format.timestamp(),
                winston.format.json(),
              ),
            }),
          ]
          : [
            new winston.transports.Console({
              level: configurations('LOG_LEVEL') || 'debug',
              format: winston.format.combine(
                winston.format.timestamp(),
                winston.format.ms(),
                nestWinstonModuleUtilities.format.nestLike(
                  configurations('APP_NAME'),
                  {
                    colors: true,
                    prettyPrint: true,
                  },
                ),
              ),
            }),
          ]),
      ],
    }),
  ],
  ...moduleConfig,
})
export class AppModule { }
