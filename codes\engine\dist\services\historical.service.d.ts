import { GetHistoricalDto } from 'src/dto/get-historical.dto';
import { HistoricalEntity } from 'src/entity/historical.entity';
import { Historical } from 'src/interface/historical.interface';
import { Repository } from 'typeorm';
import { Logger } from 'winston';
export declare class HistoricalService {
    private readonly logger;
    private readonly HistoricalsRepository;
    constructor(logger: Lo<PERSON>, HistoricalsRepository: Repository<HistoricalEntity>);
    countHistorical(symbol: string, interval: string, start?: Date, end?: Date): Promise<number>;
    getHistorical(param: GetHistoricalDto): Promise<Historical[]>;
    insertHistorical(param: Historical[]): Promise<void>;
    deleteHistorical(param: {
        symbol: string;
        interval: string;
    }): Promise<void>;
}
