"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HistoricalIngestionService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const log_detail_util_1 = require("../util/log-detail.util");
const configurations_1 = __importDefault(require("../configurations"));
const adapter_service_1 = require("./adapter.service");
const to_milliseconds_util_1 = require("../util/to-milliseconds.util");
const historical_cache_service_1 = require("./historical-cache.service");
const historical_service_1 = require("./historical.service");
const instrument_service_1 = require("./instrument.service");
const suffle_array_util_1 = require("../util/suffle-array.util");
const get_symbols_slice_util_1 = require("../util/get-symbols-slice.util");
const method_status_service_1 = require("./method-status.service");
let HistoricalIngestionService = class HistoricalIngestionService {
    logger;
    historicalService;
    methodStatusService;
    adapterService;
    historicalCacheService;
    instrumentService;
    constructor(logger, historicalService, methodStatusService, adapterService, historicalCacheService, instrumentService) {
        this.logger = logger;
        this.historicalService = historicalService;
        this.methodStatusService = methodStatusService;
        this.adapterService = adapterService;
        this.historicalCacheService = historicalCacheService;
        this.instrumentService = instrumentService;
    }
    async ingestHistorical(interval) {
        const engineMode = (0, configurations_1.default)('ENGINE_MODE');
        const disableCluster = (0, configurations_1.default)('DISABLE_CLUSTER');
        const intervals = interval ? [interval] : (0, configurations_1.default)('INTERVALS');
        const executionIntervals = (0, configurations_1.default)('EXECUTION_INTERVAL');
        if (engineMode === 'worker') {
            let symbols = await this.instrumentService.getSymbols();
            symbols = disableCluster === 'false'
                ? (0, suffle_array_util_1.shuffleArray)((0, get_symbols_slice_util_1.getSymbolsSlice)(symbols))
                : (0, suffle_array_util_1.shuffleArray)(symbols);
            await this.ingestSymbols(symbols, () => intervals);
        }
        if (engineMode === 'service') {
            const methodStatuses = await this.methodStatusService.getAllMethodStatus();
            let symbols = [...new Set(methodStatuses.map((item) => item.symbol))];
            symbols = disableCluster === 'false'
                ? (0, suffle_array_util_1.shuffleArray)((0, get_symbols_slice_util_1.getSymbolsSlice)(symbols))
                : (0, suffle_array_util_1.shuffleArray)(symbols);
            const intervals = interval ? [interval] : [...new Set(methodStatuses.map((item) => item.interval)), executionIntervals];
            await this.ingestSymbols(symbols, () => intervals);
        }
    }
    async ingestSymbols(symbols, getIntervals) {
        for (const symbol of symbols) {
            for (const interval of getIntervals(symbol)) {
                this.logger.info(`Ingesting ${symbol} ${interval}`);
                await this.process(symbol, interval);
            }
        }
    }
    ;
    async process(symbol, interval) {
        try {
            const startDate = new Date(0);
            const endDate = new Date();
            const defaultLimit = (0, configurations_1.default)('DEFAULT_LIMIT');
            const startTime = startDate.getTime();
            const endTime = endDate.getTime();
            const timeRange = (0, to_milliseconds_util_1.toMiliseconds)(interval);
            const instrument = await this.instrumentService.getInstrument({
                symbol,
            });
            if (!instrument)
                return;
            const listedTime = new Date(Number(instrument[0].listedTime)).getTime();
            const rowLengthTarget = Math.ceil((endTime - listedTime) / (0, to_milliseconds_util_1.toMiliseconds)(interval));
            for (let i = endTime; i > startTime; i -= timeRange * defaultLimit) {
                const startScan = new Date(i - timeRange * defaultLimit);
                const endScan = new Date(i);
                const existHistoricalCacheCount = await this.historicalCacheService.countHistorical(symbol, interval, startScan, endScan);
                const existHistoricalCount = await this.historicalService.countHistorical(symbol, interval, startScan, endScan);
                if (existHistoricalCacheCount === defaultLimit &&
                    existHistoricalCount === defaultLimit) {
                    const existHistoricalCacheFullCount = await this.historicalCacheService.countHistorical(symbol, interval);
                    const existHistoricalFullCount = await this.historicalService.countHistorical(symbol, interval);
                    if (existHistoricalCacheFullCount === existHistoricalFullCount &&
                        existHistoricalCacheFullCount >= rowLengthTarget) {
                        break;
                    }
                    continue;
                }
                if (existHistoricalCacheCount === defaultLimit &&
                    existHistoricalCount !== defaultLimit) {
                    const existHistoricalCache = await this.historicalCacheService.getHistorical({
                        symbol,
                        interval,
                        sort: 'DESC',
                        limit: defaultLimit,
                        start: startScan,
                        end: endScan,
                    });
                    await this.historicalService.insertHistorical(existHistoricalCache);
                    continue;
                }
                if (existHistoricalCacheCount !== defaultLimit &&
                    existHistoricalCount === defaultLimit) {
                    const existHistorical = await this.historicalService.getHistorical({
                        symbol,
                        interval,
                        sort: 'DESC',
                        limit: defaultLimit,
                        start: startScan,
                        end: endScan,
                    });
                    await this.historicalCacheService.insertHistorical(existHistorical);
                    continue;
                }
                const newHistorical = await this.adapterService.fetchBybitHistorical({
                    symbol,
                    interval,
                    sort: 'DESC',
                    limit: defaultLimit,
                    start: startScan,
                    end: endScan,
                });
                await this.historicalService.insertHistorical(newHistorical);
                await this.historicalCacheService.insertHistorical(newHistorical);
                if (existHistoricalCount === newHistorical.length &&
                    existHistoricalCacheCount === newHistorical.length) {
                    break;
                }
            }
            return;
        }
        catch (error) {
            this.logger.error('Failed to process historical data', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'process',
                param: { symbol, interval },
                error,
            }));
            throw new Error('Failed to process historical data');
        }
    }
};
exports.HistoricalIngestionService = HistoricalIngestionService;
exports.HistoricalIngestionService = HistoricalIngestionService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [winston_1.Logger,
        historical_service_1.HistoricalService,
        method_status_service_1.MethodStatusService,
        adapter_service_1.AdapterService,
        historical_cache_service_1.HistoricalCacheService,
        instrument_service_1.InstrumentService])
], HistoricalIngestionService);
//# sourceMappingURL=historical-ingestion.service.js.map