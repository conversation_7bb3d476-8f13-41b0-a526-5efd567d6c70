import { Logger } from 'winston';
import { CandlestickPatternService } from './candlestick-pattern.service';
import { Historical } from 'src/interface/historical.interface';
import { GetPatternDto } from 'src/dto/get-pattern.dto';
import { Pattern } from 'src/interface/pattern.interface';
import { HistoricalCacheService } from './historical-cache.service';
export declare class PatternService {
    private readonly logger;
    private readonly candlestickPatternService;
    private readonly historicalCacheService;
    constructor(logger: Logger, candlestickPatternService: CandlestickPatternService, historicalCacheService: HistoricalCacheService);
    getPattern(param: GetPatternDto, historical?: Historical[]): Promise<Pattern[]>;
}
