import { Logger } from 'winston';
import { AdapterService } from './adapter.service';
import { HistoricalCacheService } from './historical-cache.service';
import { HistoricalService } from './historical.service';
import { InstrumentService } from './instrument.service';
import { MethodStatusService } from './method-status.service';
export declare class HistoricalIngestionService {
    private readonly logger;
    private readonly historicalService;
    private readonly methodStatusService;
    private readonly adapterService;
    private readonly historicalCacheService;
    private readonly instrumentService;
    constructor(logger: Logger, historicalService: HistoricalService, methodStatusService: MethodStatusService, adapterService: AdapterService, historicalCacheService: HistoricalCacheService, instrumentService: InstrumentService);
    ingestHistorical(interval?: string): Promise<void>;
    private ingestSymbols;
    private process;
}
