"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MethodController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const nest_winston_1 = require("nest-winston");
const method_service_1 = require("../services/method.service");
const log_detail_util_1 = require("../util/log-detail.util");
const winston_1 = require("winston");
const get_method_dto_1 = require("../dto/get-method.dto");
let MethodController = class MethodController {
    methodService;
    logger;
    constructor(methodService, logger) {
        this.methodService = methodService;
        this.logger = logger;
    }
    async getParam(body) {
        try {
            const result = await this.methodService.getParam(body);
            return result;
        }
        catch (error) {
            const message = error?.message || 'Failed to get method param';
            this.logger.error('Failed to get method param', (0, log_detail_util_1.logDetail)({
                class: 'MethodController',
                function: 'getParam',
                error: error.stack || message,
            }));
            throw new common_1.HttpException(message, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getResult(body) {
        try {
            const result = await this.methodService.getResult(body);
            return result;
        }
        catch (error) {
            const message = error?.message || 'Failed to get method result';
            this.logger.error('Failed to get method result', (0, log_detail_util_1.logDetail)({
                class: 'MethodController',
                function: 'getResult',
                error: error.stack || message,
            }));
            throw new common_1.HttpException(message, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getPerformance(body) {
        try {
            const result = await this.methodService.getPerformance(body);
            return result;
        }
        catch (error) {
            const message = error?.message || 'Failed to get method performance';
            this.logger.error('Failed to get method performance', (0, log_detail_util_1.logDetail)({
                class: 'MethodController',
                function: 'getPerformance',
                error: error.stack || message,
            }));
            throw new common_1.HttpException(message, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.MethodController = MethodController;
__decorate([
    (0, common_1.Post)('param'),
    (0, swagger_1.ApiOperation)({ summary: 'Get Method Param' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of Method Param',
        isArray: true,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_method_dto_1.GetMethodDto]),
    __metadata("design:returntype", Promise)
], MethodController.prototype, "getParam", null);
__decorate([
    (0, common_1.Post)('result'),
    (0, swagger_1.ApiOperation)({ summary: 'Get Method Result' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of Method Result',
        isArray: true,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_method_dto_1.GetMethodDto]),
    __metadata("design:returntype", Promise)
], MethodController.prototype, "getResult", null);
__decorate([
    (0, common_1.Post)('performance'),
    (0, swagger_1.ApiOperation)({ summary: 'Get Method Performance' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of Method Performance',
        isArray: true,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_method_dto_1.GetMethodDto]),
    __metadata("design:returntype", Promise)
], MethodController.prototype, "getPerformance", null);
exports.MethodController = MethodController = __decorate([
    (0, swagger_1.ApiTags)('Method'),
    (0, common_1.Controller)('method'),
    __param(1, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [method_service_1.MethodService,
        winston_1.Logger])
], MethodController);
//# sourceMappingURL=method.controller.js.map