import { Logger } from 'winston';
import { StaticPlanService } from './static-plan.service';
import { OptimizeStaticPlanService } from './optimize-static-plan.service';
import { GetDynamicPlanDto } from 'src/dto/get-dynamic-plan.dto';
import { Plan } from 'src/interface/plan.interface';
import { InstrumentService } from './instrument.service';
import { Historical } from 'src/interface/historical.interface';
import { Pattern } from 'src/interface/pattern.interface';
import { Instrument } from 'src/interface/instrument.interface';
import { PatternService } from './pattern.service';
import { HistoricalCacheService } from './historical-cache.service';
export declare class DynamicPlanService {
    private readonly logger;
    private readonly historicalCacheService;
    private readonly staticPlanService;
    private readonly optimizeStaticPlanService;
    private readonly instrumentService;
    private readonly patternService;
    constructor(logger: Logger, historicalCacheService: HistoricalCacheService, staticPlanService: StaticPlanService, optimizeStaticPlanService: OptimizeStaticPlanService, instrumentService: InstrumentService, patternService: PatternService);
    getPlan(param: GetDynamicPlanDto, historical?: Historical[], patterns?: Pattern[], instrument?: Instrument[], staticPlan?: Plan[]): Promise<Plan[]>;
}
