"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PatternService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const candlestick_pattern_service_1 = require("./candlestick-pattern.service");
const configurations_1 = __importDefault(require("../configurations"));
const log_detail_util_1 = require("../util/log-detail.util");
const historical_cache_service_1 = require("./historical-cache.service");
let PatternService = class PatternService {
    logger;
    candlestickPatternService;
    historicalCacheService;
    constructor(logger, candlestickPatternService, historicalCacheService) {
        this.logger = logger;
        this.candlestickPatternService = candlestickPatternService;
        this.historicalCacheService = historicalCacheService;
    }
    async getPattern(param, historical) {
        try {
            const historicalData = historical ??
                (await this.historicalCacheService.getHistorical({
                    symbol: param.symbol,
                    interval: param.interval,
                    start: param.start,
                    end: param.end,
                    limit: 1000000000,
                    sort: 'ASC',
                }));
            const patternData = [];
            for (let i = (0, configurations_1.default)('PATTERN_TREND_SAMPLE_LENGTH'); i < historicalData.length; i++) {
                const pattern = this.candlestickPatternService[param.pattern]({
                    candles: historicalData.slice(i - (0, configurations_1.default)('PATTERN_TREND_SAMPLE_LENGTH'), i),
                    trend: param.trend,
                });
                if (pattern) {
                    patternData.push({
                        ...historicalData[i - 1],
                        patternType: param.patternType,
                        pattern: param.pattern,
                    });
                }
            }
            return patternData;
        }
        catch (error) {
            this.logger.error('Failed to fetch pattern data', (0, log_detail_util_1.logDetail)({
                class: 'PatternService',
                function: 'getPattern',
                param,
                error,
            }));
            throw new Error('Failed to fetch pattern data');
        }
    }
};
exports.PatternService = PatternService;
exports.PatternService = PatternService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [winston_1.Logger,
        candlestick_pattern_service_1.CandlestickPatternService,
        historical_cache_service_1.HistoricalCacheService])
], PatternService);
//# sourceMappingURL=pattern.service.js.map