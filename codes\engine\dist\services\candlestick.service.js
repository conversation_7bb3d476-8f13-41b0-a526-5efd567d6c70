"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CandlestickService = void 0;
const common_1 = require("@nestjs/common");
const configurations_1 = __importDefault(require("../configurations"));
let CandlestickService = class CandlestickService {
    bodyRange(candle) {
        return Math.abs(candle.open - candle.close);
    }
    topBodyPrice(candle) {
        return Math.max(candle.open, candle.close);
    }
    bottomBodyPrice(candle) {
        return Math.min(candle.open, candle.close);
    }
    halfBodyPrice(candle) {
        return this.bodyRange(candle) / 2 + this.bottomBodyPrice(candle);
    }
    boneRange(candle) {
        return Math.abs(candle.low - candle.high);
    }
    topBonePrice(candle) {
        return candle.high;
    }
    bottomBonePrice(candle) {
        return candle.low;
    }
    halfBonePrice(candle) {
        return this.boneRange(candle) / 2 + this.bottomBonePrice(candle);
    }
    topDiv3bonePrice(candle) {
        return (this.boneRange(candle) / 3) * 2 + this.bottomBonePrice(candle);
    }
    bottomDiv3bonePrice(candle) {
        return (this.boneRange(candle) / 3) * 1 + this.bottomBonePrice(candle);
    }
    shadowTopRange(candle) {
        return candle.close > candle.open
            ? Math.abs(candle.high - candle.close)
            : Math.abs(candle.high - candle.open);
    }
    shadowBottomRange(candle) {
        return candle.close > candle.open
            ? Math.abs(candle.low - candle.open)
            : Math.abs(candle.low - candle.close);
    }
    isGapBetweenCandles(params) {
        return (this.topBodyPrice(params.secondCandle) <
            this.bottomBodyPrice(params.firstCandle) ||
            this.bottomBodyPrice(params.secondCandle) >
                this.topBodyPrice(params.firstCandle));
    }
    isTrendForCandlestickPattern(params) {
        if (params.candles.length < (0, configurations_1.default)('PATTERN_TREND_SAMPLE_LENGTH')) {
            return false;
        }
        const trendSample = params.candles
            .slice(params.candles.length - (0, configurations_1.default)('PATTERN_TREND_SAMPLE_LENGTH'), params.candles.length)
            .map((candle) => this.halfBodyPrice(candle));
        const movingAverage = trendSample.reduce((accumulator, currentValue) => accumulator + currentValue, 0) / trendSample.length;
        const trend = movingAverage < params.candles[params.candles.length - 1].low
            ? 'up'
            : movingAverage > params.candles[params.candles.length - 1].high
                ? 'down'
                : 'neutral';
        return params.trend === trend;
    }
    isBullish(candle) {
        return candle.close > candle.open;
    }
    isBearish(candle) {
        return candle.close < candle.open;
    }
    isLongBodyGreen(candle) {
        const bodyRange = this.bodyRange(candle);
        const halfBoneRange = this.boneRange(candle) / 2;
        const boneRange = this.boneRange(candle);
        return (bodyRange >= halfBoneRange &&
            bodyRange < 0.95 * boneRange &&
            this.isBullish(candle));
    }
    isShortBodyGreen(candle) {
        const bodyRange = this.bodyRange(candle);
        const halfBoneRange = this.halfBonePrice(candle);
        const boneRange = this.boneRange(candle);
        return (bodyRange > 0 &&
            bodyRange < halfBoneRange &&
            bodyRange > 0.05 * boneRange &&
            this.isBullish(candle));
    }
    isLongBodyRed(candle) {
        const bodyRange = this.bodyRange(candle);
        const halfBoneRange = this.boneRange(candle) / 2;
        const boneRange = this.boneRange(candle);
        return (bodyRange >= halfBoneRange &&
            bodyRange < 0.95 * boneRange &&
            this.isBearish(candle));
    }
    isShortBodyRed(candle) {
        const bodyRange = this.bodyRange(candle);
        const halfBoneRange = this.halfBonePrice(candle);
        const boneRange = this.boneRange(candle);
        return (bodyRange > 0 &&
            bodyRange < halfBoneRange &&
            bodyRange > 0.05 * boneRange &&
            this.isBearish(candle));
    }
    isWithoutBody(candle) {
        const bodyRange = this.bodyRange(candle);
        const boneRange = this.boneRange(candle);
        return bodyRange <= 0.05 * boneRange;
    }
};
exports.CandlestickService = CandlestickService;
exports.CandlestickService = CandlestickService = __decorate([
    (0, common_1.Injectable)()
], CandlestickService);
//# sourceMappingURL=candlestick.service.js.map