"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MethodService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const nest_winston_1 = require("nest-winston");
const log_detail_util_1 = require("../util/log-detail.util");
const typeorm_2 = require("typeorm");
const winston_1 = require("winston");
const method_param_entity_1 = require("../entity/method-param.entity");
const method_result_entity_1 = require("../entity/method-result.entity");
const method_performance_entity_1 = require("../entity/method-performance.entity");
let MethodService = class MethodService {
    MethodParamsRepository;
    MethodResultRepository;
    MethodPerformanceRepository;
    logger;
    constructor(MethodParamsRepository, MethodResultRepository, MethodPerformanceRepository, logger) {
        this.MethodParamsRepository = MethodParamsRepository;
        this.MethodResultRepository = MethodResultRepository;
        this.MethodPerformanceRepository = MethodPerformanceRepository;
        this.logger = logger;
    }
    async deleteMethodResultNotIn(methodIds) {
        try {
            await this.MethodResultRepository.manager.transaction(async (transactionalEntityManager) => {
                await transactionalEntityManager
                    .createQueryBuilder()
                    .delete()
                    .from(method_result_entity_1.MethodResultEntity)
                    .where('methodId NOT IN (:...methodIds)', { methodIds })
                    .execute();
            });
        }
        catch (err) {
            this.logger.error('Failed to delete method result data', (0, log_detail_util_1.logDetail)({
                class: 'MethodService',
                function: 'deleteMethodResultNotIn',
                error: err,
            }));
            throw new Error('Failed to delete method result data');
        }
    }
    async deleteMethodPerformanceNotIn(methodIds) {
        try {
            await this.MethodPerformanceRepository.manager.transaction(async (transactionalEntityManager) => {
                await transactionalEntityManager
                    .createQueryBuilder()
                    .delete()
                    .from(method_performance_entity_1.MethodPerformanceEntity)
                    .where('methodId NOT IN (:...methodIds)', { methodIds })
                    .execute();
            });
        }
        catch (err) {
            this.logger.error('Failed to delete method performance data', (0, log_detail_util_1.logDetail)({
                class: 'MethodService',
                function: 'deleteMethodPerformanceNotIn',
                error: err,
            }));
            throw new Error('Failed to delete method performance data');
        }
    }
    async getMethodIds() {
        try {
            const queryBuilder = this.MethodParamsRepository.createQueryBuilder('method-param');
            return await queryBuilder.select('method-param.methodId').getMany();
        }
        catch (err) {
            this.logger.error('Failed to read method-param data', (0, log_detail_util_1.logDetail)({
                class: 'MethodService',
                function: 'getMethodIds',
                error: err,
            }));
            throw new Error(`Failed to read method-param data`);
        }
    }
    async insertMethodParam(param) {
        try {
            const result = await this.MethodParamsRepository.upsert(param, ['methodId']);
            return result;
        }
        catch (err) {
            this.logger.error('Failed to insert method-param data', (0, log_detail_util_1.logDetail)({
                class: 'MethodParamsService',
                function: 'insertMethodParam',
                error: err,
                param,
            }));
            throw new Error('Failed to insert method-param data');
        }
    }
    async insertMethodResult(param) {
        try {
            return await this.MethodResultRepository.upsert(param, ['methodId', 'date']);
        }
        catch (err) {
            this.logger.error('Failed to insert method-result data', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'insertMethodResult',
                error: err,
                param,
            }));
            throw new Error('Failed to insert method-result data');
        }
    }
    async insertMethodPerformance(param) {
        try {
            param.methodId = param.methodId.trim();
            const updateResult = await this.MethodPerformanceRepository.update({
                methodId: param.methodId,
            }, {
                fromDate: param.fromDate,
                endDate: param.endDate,
                totalValidTrade: param.totalValidTrade,
                totalInvalidTrade: param.totalInvalidTrade,
                probability: param.probability,
                averageRewardRiskRatio: param.averageRewardRiskRatio,
                maxOpenPosition: param.maxOpenPosition,
                maxConsecutiveLoss: param.maxConsecutiveLoss,
                maxConsecutiveProfit: param.maxConsecutiveProfit,
                cumulativePercentage: param.cumulativePercentage,
                maxHoldingPeriod: param.maxHoldingPeriod,
            });
            if (updateResult.affected === 0) {
                return await this.MethodPerformanceRepository.insert(param);
            }
            return updateResult;
        }
        catch (err) {
            if (err.code === '23505') {
                return await this.MethodPerformanceRepository.update({
                    methodId: param.methodId,
                    fromDate: param.fromDate,
                    endDate: param.endDate,
                }, param);
            }
            throw err;
        }
    }
    async getParam(param) {
        try {
            const queryBuilder = this.MethodParamsRepository.createQueryBuilder('method-param');
            if (param.methodId) {
                queryBuilder.andWhere('method-param.methodId = :methodId', {
                    methodId: param.methodId,
                });
            }
            return await queryBuilder.getMany();
        }
        catch (err) {
            this.logger.error('Failed to read method-param data', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'read',
                error: err,
                param,
            }));
            throw new Error(`Failed to read method-param data`);
        }
    }
    async getResult(param) {
        try {
            const queryBuilder = this.MethodResultRepository.createQueryBuilder('method-result');
            if (param.methodId) {
                queryBuilder.andWhere('method-result.methodId = :methodId', {
                    methodId: param.methodId,
                });
            }
            return await queryBuilder.getMany();
        }
        catch (err) {
            this.logger.error('Failed to read method-result data', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'getResult',
                error: err,
                param,
            }));
            throw new Error(`Failed to read method-result data`);
        }
    }
    async getPerformance(param) {
        try {
            const queryBuilder = this.MethodPerformanceRepository.createQueryBuilder('method-performance');
            if (param.methodId) {
                queryBuilder.andWhere('method-performance.methodId = :methodId', {
                    methodId: param.methodId,
                });
            }
            return await queryBuilder.getMany();
        }
        catch (err) {
            this.logger.error('Failed to read method-performance data', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'getPerformance',
                error: err,
                param,
            }));
            throw new Error(`Failed to read method-performance data`);
        }
    }
    async getMethodsByPerformance(param) {
        const { minProbability, methodLimit } = param;
        try {
            const rawResults = await this.MethodPerformanceRepository.query(`
          SELECT "methodId", "probability"
          FROM (
            SELECT   
              mp2."methodId", mp."probability",
              ROW_NUMBER() OVER (
                PARTITION BY mp2."symbol", mp2."interval", mp2."orderType", mp2."pattern"
                ORDER BY mp."probability" DESC
              ) AS rn
            FROM public."method-performance" mp  
            JOIN public."method-param" mp2   
              ON mp."methodId" = mp2."methodId"  
            WHERE mp."probability" > $1
          ) AS sub
          WHERE rn = 1
          ORDER BY "probability" DESC
          LIMIT $2;
          `, [minProbability ?? 70, methodLimit ?? 1000]);
            return rawResults.map((item) => item.methodId);
        }
        catch (err) {
            this.logger.error('Failed to read method-performance data', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'getMethodsByPerformance',
                error: err,
                param,
            }));
            throw new Error('Failed to read method-performance data');
        }
    }
    async getMultiMethodResult(param) {
        try {
            const methodIds = await this.getMethodsByPerformance(param);
            if (methodIds.length === 0) {
                return [];
            }
            const queryBuilder = this.MethodResultRepository.createQueryBuilder('method-result');
            queryBuilder.where('method-result.methodId IN (:...methodIds)', {
                methodIds,
            });
            queryBuilder.orderBy('method-result.date', 'ASC');
            const result = await queryBuilder.getMany();
            return result.map((item) => ({
                ...item,
                date: new Date(item.date),
                openDate: item.openDate ? new Date(item.openDate) : null,
                closedDate: item.closedDate ? new Date(item.closedDate) : null,
                expiryDate: new Date(item.expiryDate),
            }));
        }
        catch (err) {
            this.logger.error('Failed to read method-result data', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'getResultsByMethods',
                error: err,
                param,
            }));
            throw new Error('Failed to read method-result data');
        }
    }
    async deleteMethodResult(methodId) {
        try {
            await this.MethodResultRepository.delete({ methodId });
        }
        catch (err) {
            this.logger.error('Failed to delete method-result data', (0, log_detail_util_1.logDetail)({
                class: 'MethodService',
                function: 'deleteMethodResult',
                error: err,
            }));
            throw new Error(`Failed to delete method-result data`);
        }
    }
    async deleteMethodPerformance(methodId) {
        try {
            await this.MethodPerformanceRepository.delete({ methodId });
        }
        catch (err) {
            this.logger.error('Failed to delete method-performance data', (0, log_detail_util_1.logDetail)({
                class: 'MethodService',
                function: 'deleteMethodPerformance',
                error: err,
            }));
            throw new Error(`Failed to delete method-performance data`);
        }
    }
    async deleteMethodParam(methodId) {
        try {
            await this.MethodParamsRepository.delete({ methodId });
        }
        catch (err) {
            this.logger.error('Failed to delete method-param data', (0, log_detail_util_1.logDetail)({
                class: 'MethodService',
                function: 'deleteMethodParam',
                error: err,
            }));
            throw new Error(`Failed to delete method-param data`);
        }
    }
};
exports.MethodService = MethodService;
exports.MethodService = MethodService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(method_param_entity_1.MethodParamEntity)),
    __param(1, (0, typeorm_1.InjectRepository)(method_result_entity_1.MethodResultEntity)),
    __param(2, (0, typeorm_1.InjectRepository)(method_performance_entity_1.MethodPerformanceEntity)),
    __param(3, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        winston_1.Logger])
], MethodService);
//# sourceMappingURL=method.service.js.map