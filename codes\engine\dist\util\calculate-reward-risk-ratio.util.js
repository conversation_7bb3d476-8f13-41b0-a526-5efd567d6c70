"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.calculateRewardRiskRatio = calculateRewardRiskRatio;
function calculateRewardRiskRatio(param) {
    const validTrade = param.filter((item) => item.status === 'profit' || item.status === 'loss');
    const totalRatio = validTrade.reduce((acc, cur) => {
        acc += Math.abs(cur.profitPercent) / Math.abs(cur.stopPercent);
        return acc;
    }, 0);
    return Number((totalRatio / validTrade.length).toFixed(2));
}
//# sourceMappingURL=calculate-reward-risk-ratio.util.js.map