export interface BacktestPerformance {
    methodId: string;
    fromDate: Date;
    endDate: Date;
    totalValidTrade: number;
    totalInvalidTrade: number;
    averageRewardRiskRatio: number;
    probability: number;
    maxOpenPosition: number;
    maxStopPercent: number;
    maxProfitPercent: number;
    maxConsecutiveLoss: number;
    maxConsecutiveProfit: number;
    maxHoldingPeriod: number;
    cumulativePercentage: number;
}
