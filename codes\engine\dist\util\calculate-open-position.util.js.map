{"version": 3, "file": "calculate-open-position.util.js", "sourceRoot": "", "sources": ["../../src/util/calculate-open-position.util.ts"], "names": [], "mappings": ";;AAEA,sDA4BC;AA5BD,SAAgB,qBAAqB,CAAC,KAAqB;IACzD,MAAM,cAAc,GAAG,IAAI,GAAG,EAAkB,CAAC;IACjD,MAAM,WAAW,GAAG,IAAI,GAAG,EAAE,GAAG,CAAC,CAAC;IAElC,KAAK,MAAM,MAAM,IAAI,KAAK,EAAE,CAAC;QAC3B,IAAI,CAAC,MAAM,CAAC,UAAU;YAAE,SAAS;QAEjC,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;QACpC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,UAAU,IAAI,IAAI,IAAI,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC;QAExD,IAAI,GAAG,GAAG,KAAK;YAAE,SAAS;QAE1B,MAAM,QAAQ,GAAG,GAAG,GAAG,KAAK,CAAC;QAC7B,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,WAAW,CAAC,CAAC;QAEzD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,aAAa,EAAE,CAAC,EAAE,EAAE,CAAC;YACxC,MAAM,SAAS,GAAG,KAAK,GAAG,CAAC,GAAG,WAAW,CAAC;YAC1C,MAAM,YAAY,GAAG,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YACxD,cAAc,CAAC,GAAG,CAAC,SAAS,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAED,IAAI,GAAG,GAAG,CAAC,CAAC;IACZ,KAAK,MAAM,KAAK,IAAI,cAAc,CAAC,MAAM,EAAE,EAAE,CAAC;QAC5C,IAAI,KAAK,GAAG,GAAG;YAAE,GAAG,GAAG,KAAK,CAAC;IAC/B,CAAC;IAED,OAAO,GAAG,CAAC;AACb,CAAC"}