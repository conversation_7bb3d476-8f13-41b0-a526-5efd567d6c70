import { MethodType } from 'src/interface/method-type.interface';
export declare abstract class MethodParamEntity {
    methodId: string;
    symbol: string;
    interval: string;
    start: Date;
    end: Date;
    limit: number;
    sort: 'ASC' | 'DESC';
    trend: string;
    patternType: string;
    pattern: string;
    orderType: string;
    validityPeriod: number;
    entryPercentByClose: number;
    riskPercent: number;
    rewardPercent: number;
    lookbackPeriod: number;
    methodType: MethodType;
    enableOptimization: boolean;
}
