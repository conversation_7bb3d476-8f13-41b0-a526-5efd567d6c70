import { GetStaticPlanDto } from 'src/dto/get-static-plan.dto';
import { OptimizedStaticPlan } from 'src/interface/optimized-static-plan.interface';
import { Plan } from 'src/interface/plan.interface';
import { OptimizeStaticPlanService } from 'src/services/optimize-static-plan.service';
import { Logger } from 'winston';
export declare class OptimizeStaticPlanController {
    private readonly appService;
    private readonly logger;
    constructor(appService: OptimizeStaticPlanService, logger: Logger);
    getOptimizedParam(body: GetStaticPlanDto): Promise<OptimizedStaticPlan>;
    getOptimizedStaticPlan(body: GetStaticPlanDto): Promise<Plan[]>;
}
