import { AuctionFeeInfo } from './auction-fee-info.interface';
import { LeverageFilter } from './leverage-filter.interface';
import { LotSizeFilter } from './lot-size-filter.interface';
import { PriceFilter } from './price-filter.interface';
export interface Instrument {
    symbol: string;
    launchTime: number;
    listedTime: number;
    priceScale?: number;
    leverageFilter: LeverageFilter;
    priceFilter: PriceFilter;
    lotSizeFilter: LotSizeFilter;
    fundingInterval?: number;
    upperFundingRate?: number;
    lowerFundingRate?: number;
    auctionFeeInfo?: AuctionFeeInfo;
}
