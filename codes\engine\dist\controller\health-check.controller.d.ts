import { Logger } from 'winston';
import { HealthCheckService } from 'src/services/health-check.service';
export declare class HealthCheckController {
    private readonly healthCheckService;
    private readonly logger;
    constructor(healthCheckService: HealthCheckService, logger: Logger);
    check(): Promise<{
        status: string;
        timestamp: string;
        results: Record<string, 'reachable' | 'unreachable'>;
        message?: string;
    }>;
}
