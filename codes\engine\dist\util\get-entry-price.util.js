"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getEntry = getEntry;
const round_to_tick_size_util_1 = require("./round-to-tick-size.util");
function getEntry(param) {
    const price = param.closePrice * (1 + param.entryPercentByClose / 100);
    return (0, round_to_tick_size_util_1.roundToTickSize)({
        mathRound: param.orderType === 'long' ? 'ceil' : 'floor',
        price,
        tickSize: param.tickSize,
    });
}
//# sourceMappingURL=get-entry-price.util.js.map