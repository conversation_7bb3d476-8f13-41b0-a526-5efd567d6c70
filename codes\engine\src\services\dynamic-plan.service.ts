import { Inject, Injectable } from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';

import { StaticPlanService } from './static-plan.service';
import { OptimizeStaticPlanService } from './optimize-static-plan.service';
import { BacktestService } from './backtest.service';
import { GetDynamicPlanDto } from 'src/dto/get-dynamic-plan.dto';
import { Plan } from 'src/interface/plan.interface';
import { InstrumentService } from './instrument.service';
import { toMiliseconds } from 'src/util/to-milliseconds.util';
import { getExpiryDate } from 'src/util/get-expiry-date.util';
import { getEntry } from 'src/util/get-entry-price.util';
import { getTargetPrice } from 'src/util/get-target-price.util';
import { logDetail } from 'src/util/log-detail.util';
import { Historical } from 'src/interface/historical.interface';
import { Pattern } from 'src/interface/pattern.interface';
import { Instrument } from 'src/interface/instrument.interface';
import { PatternService } from './pattern.service';
import configurations from 'src/configurations';
import { runGenerateResult } from 'src/util/run-generate-result.util';
import { HistoricalCacheService } from './historical-cache.service';

@Injectable()
export class DynamicPlanService {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    private readonly historicalCacheService: HistoricalCacheService,
    private readonly staticPlanService: StaticPlanService,
    private readonly optimizeStaticPlanService: OptimizeStaticPlanService,
    private readonly instrumentService: InstrumentService,
    private readonly patternService: PatternService,
  ) {}

  async getPlan(
    param: GetDynamicPlanDto,
    historical?: Historical[],
    patterns?: Pattern[],
    instrument?: Instrument[],
    staticPlan?: Plan[],
  ): Promise<Plan[]> {
    try {
      const planData: Plan[] = [];
      const lookbackPeriod =
        param.lookbackPeriod ?? configurations('DEFAULT_LOOKBACK_PERIOD');

      const historicalData =
        historical ?? (await this.historicalCacheService.getHistorical(param));

      const patternData =
        patterns ??
        (await this.patternService.getPattern(param, historicalData));

      const instrumentData =
        instrument ??
        (await this.instrumentService.getInstrument({
          symbol: param.symbol,
        }));

      const staticPlanData =
        staticPlan ??
        (await this.staticPlanService.getPlan(
          param,
          historicalData,
          patternData,
          instrumentData,
        ));

      const historicalDataExecution =
        await this.historicalCacheService.getHistorical({
          ...param,
          start: new Date(staticPlanData[0].date),
          interval: configurations('EXECUTION_INTERVAL'),
        });

      if (!staticPlanData.length) return [];

      if (!instrumentData.length) return [];
      const tickSize = instrumentData[0].priceFilter.tickSize;

      for (const item of staticPlanData) {
        const startDate = new Date(
          item.date.getTime() -
            toMiliseconds(item.interval) * (lookbackPeriod + 1),
        );

        const planSample = staticPlanData.filter(
          (p) =>
            p.date.getTime() >= startDate.getTime() &&
            p.date.getTime() < item.date.getTime(),
        );
        if (!planSample.length) continue;
        const backtestResult = runGenerateResult(
          planSample,
          historicalDataExecution,
        );
        const totalProfit = backtestResult.filter(
          (item) => item.status === 'profit',
        ).length;
        const totalLoss = backtestResult.filter(
          (item) => item.status === 'loss',
        ).length;
        const totalResults = totalProfit + totalLoss;
        if (
          !backtestResult.length ||
          (totalProfit / totalResults) * 100 <
            configurations('METHOD_MIN_PROBABILITY')
        )
          continue;

        const optimizePlan = await this.optimizeStaticPlanService.optimize(
          param,
          historicalData,
          historicalDataExecution,
          patternData,
          instrumentData,
          planSample,
          backtestResult,
        );

        const expiryDate = getExpiryDate(item);

        const entry = getEntry({
          orderType: item.orderType,
          closePrice: item.entry * (1 / (1 + item.entryPercentByClose / 100)),
          entryPercentByClose: optimizePlan.entryPercentByClose,
          tickSize,
        });

        const stopLoss = getTargetPrice({
          mathRound: item.orderType === 'long' ? 'floor' : 'ceil',
          entry,
          targetPercent: optimizePlan.riskPercent,
          tickSize,
        });

        const takeProfit = getTargetPrice({
          mathRound: item.orderType === 'long' ? 'floor' : 'ceil',
          entry,
          targetPercent: optimizePlan.rewardPercent,
          tickSize,
        });

        const plan = {
          ...item,
          expiryDate,
          entryPercentByClose: optimizePlan.entryPercentByClose,
          entry,
          riskPercent: optimizePlan.riskPercent,
          stopLoss,
          rewardPercent: optimizePlan.rewardPercent,
          takeProfit,
          stopPercent: ((stopLoss - entry) / entry) * 100,
          profitPercent: ((takeProfit - entry) / entry) * 100,
        };
        planData.push(plan);
      }

      return planData;
    } catch (error) {
      this.logger.error(
        'Failed to fetch pattern data',
        logDetail({
          class: 'AppService',
          function: 'getPlan',
          param,
          error,
        }),
      );
      throw new Error('Failed to fetch pattern data');
    }
  }
}
