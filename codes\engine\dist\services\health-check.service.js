"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var HealthCheckService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.HealthCheckService = void 0;
const common_1 = require("@nestjs/common");
const axios_1 = __importDefault(require("axios"));
const configurations_1 = __importDefault(require("../configurations"));
const nest_winston_1 = require("nest-winston");
const log_detail_util_1 = require("../util/log-detail.util");
const typeorm_1 = require("typeorm");
let HealthCheckService = class HealthCheckService {
    static { HealthCheckService_1 = this; }
    logger;
    dataSource;
    static TIMEOUT_MS = 3000;
    healthCheckUrls = [(0, configurations_1.default)('HOST_INSTRUMENT_API')];
    constructor(logger, dataSource) {
        this.logger = logger;
        this.dataSource = dataSource;
    }
    async checkDatabase() {
        try {
            if (!this.dataSource.isInitialized) {
                throw new Error('Database connection is not initialized');
            }
            await this.dataSource.query('SELECT 1');
            return 'reachable';
        }
        catch (err) {
            const error = err;
            this.logger.error(`Database connection check failed: ${error.message}`, (0, log_detail_util_1.logDetail)({
                class: 'HealthCheckService',
                function: 'checkDatabase',
                error,
            }));
            return 'unreachable';
        }
    }
    async checkUrl(url) {
        try {
            const response = await axios_1.default.head(url, {
                timeout: HealthCheckService_1.TIMEOUT_MS,
            });
            if (response.status === 200) {
                return 'reachable';
            }
            this.logger.warn(`Unexpected status ${response.status} from ${url}`, (0, log_detail_util_1.logDetail)({
                class: 'HealthCheckService',
                function: 'checkUrl',
                url,
                status: response.status,
            }));
        }
        catch (err) {
            const error = err;
            this.logger.error(`Health check failed for ${url}: ${error.message}`, (0, log_detail_util_1.logDetail)({
                class: 'HealthCheckService',
                function: 'checkUrl',
                url,
                error: error.toJSON ? error.toJSON() : error,
            }));
        }
        return 'unreachable';
    }
    async check() {
        const timestamp = new Date().toISOString();
        const results = {};
        results['database'] = await this.checkDatabase();
        for (const url of this.healthCheckUrls) {
            results[url] = await this.checkUrl(url);
        }
        return { timestamp, results };
    }
};
exports.HealthCheckService = HealthCheckService;
exports.HealthCheckService = HealthCheckService = HealthCheckService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [common_1.Logger,
        typeorm_1.DataSource])
], HealthCheckService);
//# sourceMappingURL=health-check.service.js.map