import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import configurations from 'src/configurations';
import { HistoricalIngestionService } from './historical-ingestion.service';
import { InstrumentIngestionService } from './instrument-ingestion.service';
import { MethodStatusService } from './method-status.service';
import { MethodUpdaterService } from './method-updater.service';
import { MethodIngestionService } from './method-ingestion.service';
import { InstrumentService } from './instrument.service';
import { shuffleArray } from 'src/util/suffle-array.util';
import { getSymbolsSlice } from 'src/util/get-symbols-slice.util';

@Injectable()
export class TaskService implements OnModuleInit {
  private readonly logger = new Logger(TaskService.name);
  private isInitiated = false

  constructor(
    private readonly historicalIngestionService: HistoricalIngestionService,
    private readonly methodStatusService: MethodStatusService,
    private readonly instrumentIngestionService: InstrumentIngestionService,
    private readonly methodUpdaterService: MethodUpdaterService,
    private readonly methodIngestionService: MethodIngestionService,
    private readonly instrumentService: InstrumentService,
  ) { }

  async onModuleInit() {
    const engineMode = configurations('ENGINE_MODE');
    if (engineMode === 'worker') {
      await this.instrumentIngestionService.ingest();
      await this.historicalIngestionService.ingestHistorical();
      this.isInitiated = true

      const disableCluster = configurations('DISABLE_CLUSTER');
      let symbols = await this.instrumentService.getSymbols();
      symbols = disableCluster === 'false'
        ? shuffleArray(getSymbolsSlice(symbols))
        : shuffleArray(symbols);
      this.logger.log('Method ingestion loop triggered.');
      this.methodIngestionService.ingestForever(symbols);

    } if (engineMode === 'service') {
      (async () => {
        await this.instrumentIngestionService.ingest();
        await this.historicalIngestionService.ingestHistorical();
        this.isInitiated = true
      })()
    }
  }

  @Cron('0 */4 * * *')
  async handleInstrumentStatus() {
    if (!this.isInitiated) return
    this.logger.log('Instrument ingestion job triggered.');
    await this.instrumentIngestionService.ingest();
    this.logger.log('Historical ingestion job triggered.');
    await this.historicalIngestionService.ingestHistorical()
  }

  @Cron('* * * * *')
  async handleInterval() {
    if (!this.isInitiated) return
    const now = new Date();
    const minutes = now.getMinutes();
    const hours = now.getHours();
    const dayOfMonth = now.getDate();
    const dayOfWeek = now.getDay();
    const intervals = configurations('INTERVALS');
    const engineMode = configurations('ENGINE_MODE');

    for (const interval of intervals) {
      try {
        switch (interval) {
          case 'M':
            if (minutes === 0 && hours === 0 && dayOfMonth === 1) {
              if (engineMode === 'worker') break;
              this.logger.log('Monthly job triggered.');
              this.logger.log('Historical ingestion job triggered.');
              await this.historicalIngestionService.ingestHistorical(interval);
              this.logger.log('Method update job triggered.');
              await this.methodStatusService.resetMethodStatus(interval);
              await this.methodUpdaterService.updateMethod();
            }
            break;
          case 'W':
            if (minutes === 0 && hours === 0 && dayOfWeek === 1) {
              this.logger.log('Weekly job triggered.');
              if (engineMode === 'worker') {
                this.logger.log('Historical ingestion job triggered.');
                await this.historicalIngestionService.ingestHistorical();
                break
              }
              await this.historicalIngestionService.ingestHistorical(interval);
              await this.methodStatusService.resetMethodStatus(interval);
              await this.methodUpdaterService.updateMethod();
            }
            break;
          case 'D':
            if (minutes === 0 && hours === 0) {
              if (engineMode === 'worker') break;
              this.logger.log('Daily job triggered.');
              await this.historicalIngestionService.ingestHistorical(interval);
              await this.methodStatusService.resetMethodStatus(interval)
              await this.methodUpdaterService.updateMethod();
            }
            break;
          default:
            const intervalMinutes = parseInt(interval);
            if (isNaN(intervalMinutes)) {
              this.logger.warn(`Invalid interval: ${interval}`);
              continue;
            }

            if (
              intervalMinutes >= 60 &&
              minutes === 0 &&
              hours % (intervalMinutes / 60) === 0
            ) {
              if (engineMode === 'worker') break;
              this.logger.log(
                `Interval ${intervalMinutes / 60}h reset triggered.`,
              );
              await this.historicalIngestionService.ingestHistorical(interval);
              await this.methodStatusService.resetMethodStatus(interval);
              await this.methodUpdaterService.updateMethod();
            } else if (
              intervalMinutes < 60 &&
              minutes % intervalMinutes === 0
            ) {
              if (engineMode === 'worker') break;
              this.logger.log(`Interval ${interval}m job triggered.`);
              await this.historicalIngestionService.ingestHistorical(interval);
              await this.methodStatusService.resetMethodStatus(interval);
              await this.methodUpdaterService.updateMethod();
            }
            break;
        }
      } catch (err) {
        this.logger.error(`Error during reset for interval ${interval}:`, err);
      }
    }
  }
}
