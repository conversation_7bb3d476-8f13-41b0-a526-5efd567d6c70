"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTargetPrice = getTargetPrice;
const round_to_tick_size_util_1 = require("./round-to-tick-size.util");
function getTargetPrice(param) {
    const price = param.entry * (1 + param.targetPercent / 100);
    return (0, round_to_tick_size_util_1.roundToTickSize)({
        mathRound: param.mathRound,
        price,
        tickSize: param.tickSize,
    });
}
//# sourceMappingURL=get-target-price.util.js.map