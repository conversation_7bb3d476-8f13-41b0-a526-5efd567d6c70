"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getExpiryDate = getExpiryDate;
function getExpiryDate(param) {
    const minute = 1000 * 60;
    const expiryDate = new Date(param.date);
    const interval = parseInt(param.interval) || 1;
    switch (param.interval) {
        case 'M':
            expiryDate.setMonth(expiryDate.getDate() + param.validityPeriod * interval);
            break;
        case 'W':
            expiryDate.setDate(expiryDate.getDate() + param.validityPeriod * 7 * interval);
            break;
        case 'D':
            expiryDate.setDate(expiryDate.getDate() + param.validityPeriod * interval);
            break;
        default:
            expiryDate.setTime(expiryDate.getTime() + param.validityPeriod * minute * interval);
            break;
    }
    return expiryDate;
}
//# sourceMappingURL=get-expiry-date.util.js.map