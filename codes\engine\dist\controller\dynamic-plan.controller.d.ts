import { Logger } from 'winston';
import { DynamicPlanService } from 'src/services/dynamic-plan.service';
import { GetDynamicPlanDto } from 'src/dto/get-dynamic-plan.dto';
import { Plan } from 'src/interface/plan.interface';
export declare class DynamicPlanController {
    private readonly appService;
    private readonly logger;
    constructor(appService: DynamicPlanService, logger: Logger);
    getDynamicPlan(body: GetDynamicPlanDto): Promise<Plan[]>;
}
