"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MethodStatusEntity = void 0;
const typeorm_1 = require("typeorm");
let MethodStatusEntity = class MethodStatusEntity {
    methodId;
    symbol;
    interval;
    updatedAt;
    isUpdated;
};
exports.MethodStatusEntity = MethodStatusEntity;
__decorate([
    (0, typeorm_1.PrimaryColumn)({ type: 'varchar', nullable: false }),
    __metadata("design:type", String)
], MethodStatusEntity.prototype, "methodId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 20 }),
    __metadata("design:type", String)
], MethodStatusEntity.prototype, "symbol", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 10 }),
    __metadata("design:type", String)
], MethodStatusEntity.prototype, "interval", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: false }),
    __metadata("design:type", Date)
], MethodStatusEntity.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: false, type: 'boolean' }),
    __metadata("design:type", Boolean)
], MethodStatusEntity.prototype, "isUpdated", void 0);
exports.MethodStatusEntity = MethodStatusEntity = __decorate([
    (0, typeorm_1.Entity)('method-status'),
    (0, typeorm_1.Index)(['methodId'])
], MethodStatusEntity);
//# sourceMappingURL=method-status.entity.js.map