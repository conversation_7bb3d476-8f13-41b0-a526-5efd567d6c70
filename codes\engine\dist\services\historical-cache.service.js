"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HistoricalCacheService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const log_detail_util_1 = require("../util/log-detail.util");
const winston_1 = require("winston");
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
let HistoricalCacheService = class HistoricalCacheService {
    logger;
    constructor(logger) {
        this.logger = logger;
    }
    getFilePath(symbol, interval) {
        return path.join('data/historical', symbol, `${interval}.json`);
    }
    async writeWithRetry(filePath, data, retries = 3) {
        const tempFilePath = `${filePath}.tmp`;
        for (let i = 0; i < retries; i++) {
            try {
                fs.writeFileSync(tempFilePath, JSON.stringify(data, null, 2), 'utf-8');
                fs.renameSync(tempFilePath, filePath);
                return;
            }
            catch (err) {
                if (i === retries - 1)
                    throw err;
                await new Promise((resolve) => setTimeout(resolve, 100 * (i + 1)));
            }
            finally {
                if (fs.existsSync(tempFilePath)) {
                    try {
                        fs.unlinkSync(tempFilePath);
                    }
                    catch (cleanupErr) {
                        this.logger.warn('Failed to cleanup temp file', (0, log_detail_util_1.logDetail)({
                            class: 'HistoricalCacheService',
                            function: 'writeWithRetry',
                            error: cleanupErr,
                            param: tempFilePath,
                        }));
                    }
                }
            }
        }
    }
    sanitizeJson(raw) {
        return (raw
            .trim()
            .replace(/,\s*([}\]])/g, '$1')
            .replace(/^\uFEFF/, ''));
    }
    readJsonFileSafe(filePath) {
        if (!fs.existsSync(filePath))
            return null;
        try {
            const content = fs.readFileSync(filePath, 'utf-8');
            const sanitized = this.sanitizeJson(content);
            return sanitized ? JSON.parse(sanitized) : null;
        }
        catch (err) {
            return null;
        }
    }
    async validateCache(symbol, interval) {
        const filePath = this.getFilePath(symbol, interval);
        if (!fs.existsSync(filePath))
            return true;
        try {
            const content = this.readJsonFileSafe(filePath);
            if (content === null || !Array.isArray(content)) {
                await this.writeWithRetry(filePath, []);
                return false;
            }
            return true;
        }
        catch (err) {
            this.logger.warn(`Repairing corrupted cache file ${filePath}`);
            await this.writeWithRetry(filePath, []);
            return false;
        }
    }
    async getHistorical(param) {
        const { symbol, interval, start, end, limit, sort } = param;
        if (!symbol || !interval)
            return [];
        const filePath = this.getFilePath(symbol, interval);
        if (!fs.existsSync(filePath))
            return [];
        const stats = fs.statSync(filePath);
        if (stats.size === 0) {
            this.logger.warn(`Empty historical cache file for ${symbol}-${interval}`);
            return [];
        }
        try {
            const content = this.readJsonFileSafe(filePath);
            if (!content || !Array.isArray(content)) {
                this.logger.warn(`Expected array in historical cache file but got ${typeof content}`, (0, log_detail_util_1.logDetail)({ param: { symbol, interval, filePath } }));
                return [];
            }
            let data = content
                .filter((d) => d?.date && d?.symbol && d?.interval)
                .map((d) => {
                const parsedDate = new Date(d.date);
                return {
                    ...d,
                    date: isNaN(parsedDate.getTime()) ? new Date(0) : parsedDate,
                };
            });
            if (start) {
                data = data.filter((d) => d.date >= start);
            }
            if (end) {
                data = data.filter((d) => d.date <= end);
            }
            data.sort((a, b) => sort === 'DESC'
                ? b.date.getTime() - a.date.getTime()
                : a.date.getTime() - b.date.getTime());
            if (limit) {
                data = data.slice(0, limit);
            }
            return data;
        }
        catch (err) {
            await this.validateCache(symbol, interval);
            this.logger.error('Failed to read historical data', (0, log_detail_util_1.logDetail)({
                class: 'HistoricalCacheService',
                function: 'getHistorical',
                error: err,
                param,
            }));
            return [];
        }
    }
    async insertHistorical(param) {
        if (!param.length)
            return;
        const symbol = param[0].symbol;
        const interval = param[0].interval;
        const dirPath = path.join('data/historical', symbol);
        const filePath = this.getFilePath(symbol, interval);
        fs.mkdirSync(dirPath, { recursive: true });
        if (fs.existsSync(filePath)) {
            try {
                const content = this.readJsonFileSafe(filePath);
                if (content === null) {
                    const backupPath = `${filePath}.corrupted.${Date.now()}`;
                    fs.copyFileSync(filePath, backupPath);
                    this.logger.warn(`Backed up corrupted file to ${backupPath}`);
                }
            }
            catch (err) {
                const backupPath = `${filePath}.corrupted.${Date.now()}`;
                fs.copyFileSync(filePath, backupPath);
                this.logger.warn(`Backed up corrupted file to ${backupPath}`);
            }
        }
        let existingData = [];
        if (fs.existsSync(filePath)) {
            const content = this.readJsonFileSafe(filePath);
            if (content && Array.isArray(content)) {
                existingData = content;
            }
        }
        const map = new Map();
        [...existingData, ...param].forEach((item) => {
            map.set(new Date(item.date).toISOString(), item);
        });
        const merged = Array.from(map.values()).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
        await this.writeWithRetry(filePath, merged);
    }
    async deleteHistorical(param) {
        const filePath = this.getFilePath(param.symbol, param.interval);
        if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
        }
    }
    async countHistorical(symbol, interval, start, end) {
        const filePath = this.getFilePath(symbol, interval);
        if (!fs.existsSync(filePath))
            return 0;
        try {
            let content = this.readJsonFileSafe(filePath).map((d) => {
                const parsedDate = new Date(d.date);
                return {
                    ...d,
                    date: isNaN(parsedDate.getTime()) ? new Date(0) : parsedDate,
                };
            });
            if (!content || !Array.isArray(content))
                return 0;
            if (start) {
                content = content.filter((d) => d.date >= start);
            }
            if (end) {
                content = content.filter((d) => d.date <= end);
            }
            const count = content.length;
            return count;
        }
        catch (err) {
            await this.validateCache(symbol, interval);
            this.logger.error('Failed to count historical cache', (0, log_detail_util_1.logDetail)({
                class: 'HistoricalCacheService',
                function: 'countHistorical',
                error: err,
                param: { symbol, interval, start, end },
            }));
            return 0;
        }
    }
    async cleanupOldBackups(maxAgeDays = 7) {
        const now = Date.now();
        const maxAgeMs = maxAgeDays * 24 * 60 * 60 * 1000;
        if (!fs.existsSync('data/historical'))
            return;
        const symbolDirs = fs.readdirSync('data/historical');
        for (const symbolDir of symbolDirs) {
            const symbolPath = path.join('data/historical', symbolDir);
            const files = fs.readdirSync(symbolPath);
            for (const file of files) {
                if (file.includes('.corrupted.')) {
                    const filePath = path.join(symbolPath, file);
                    try {
                        const stats = fs.statSync(filePath);
                        if (now - stats.mtimeMs > maxAgeMs) {
                            fs.unlinkSync(filePath);
                            this.logger.debug(`Cleaned up old backup: ${filePath}`);
                        }
                    }
                    catch (err) {
                        this.logger.warn(`Failed to cleanup backup ${filePath}`, (0, log_detail_util_1.logDetail)({ error: err }));
                    }
                }
            }
        }
    }
};
exports.HistoricalCacheService = HistoricalCacheService;
exports.HistoricalCacheService = HistoricalCacheService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [winston_1.Logger])
], HistoricalCacheService);
//# sourceMappingURL=historical-cache.service.js.map