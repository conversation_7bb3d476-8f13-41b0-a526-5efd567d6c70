import { Inject, Injectable } from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import { BacktestService } from './backtest.service';
import { logDetail } from 'src/util/log-detail.util';
import { BacktestMultiPerformance } from 'src/interface/backtest-multi-performance.interface';
import { MethodResult } from 'src/interface/method-result.interface';
import { GetBacktestMultiMethodDto } from 'src/dto/get-backtest-multi-method.dto';
import { MethodService } from './method.service';

@Injectable()
export class BacktestMultiMethodService {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    private readonly backtestService: BacktestService,
    private readonly methodService: MethodService,
  ) { }

  async getBacktestMultiMethodResult(
    param: GetBacktestMultiMethodDto,
  ): Promise<MethodResult[]> {
    try {
      const results = await this.methodService.getMultiMethodResult(param);

      return param.pendingResultOnly
        ? results.filter((item) => item.status === 'pending')
        : results;
    } catch (error) {
      this.logger.error(
        'Failed to generate backtest method result',
        logDetail({
          class: 'BacktestMethodService',
          function: 'getBacktestMethodResult',
          param,
          error,
        }),
      );
      throw new Error('Failed to generate backtest method');
    }
  }

  async getBacktestMultiMethodPerformance(
    param: GetBacktestMultiMethodDto,
  ): Promise<BacktestMultiPerformance> {
    try {
      const results = await this.methodService.getMultiMethodResult(param);
      const totalMethod = [...new Set(results.map((item) => item.methodId))]
        .length;
      const performance = await this.backtestService.getPerformance(results);
      return {
        totalMethod,
        fromDate: performance.fromDate,
        endDate: performance.endDate,
        totalValidTrade: performance.totalValidTrade,
        totalInvalidTrade: performance.totalInvalidTrade,
        averageRewardRiskRatio: performance.averageRewardRiskRatio,
        probability: performance.probability,
        maxOpenPosition: performance.maxOpenPosition,
        maxStopPercent: performance.maxStopPercent,
        maxProfitPercent: performance.maxProfitPercent,
        maxConsecutiveLoss: performance.maxConsecutiveLoss,
        maxConsecutiveProfit: performance.maxConsecutiveProfit,
        maxHoldingPeriod: performance.maxHoldingPeriod,
        cumulativePercentage: performance.cumulativePercentage,
      };
    } catch (error) {
      this.logger.error(
        'Failed to generate backtest method performance',
        logDetail({
          class: 'BacktestMethodService',
          function: 'getBacktestMethodPerformance',
          param,
          error,
        }),
      );
      throw new Error('Failed to generate backtest method');
    }
  }

  async getBacktestMutilMethodBoth(param: GetBacktestMultiMethodDto): Promise<{
    result: MethodResult[];
    performance: BacktestMultiPerformance;
  }> {
    try {
      const results = await this.methodService.getMultiMethodResult(param);
      const totalMethod = [...new Set(results.map((item) => item.methodId))]
        .length;
      const performance = await this.backtestService.getPerformance(results);
      return {
        result: param.pendingResultOnly
          ? results.filter((item) => item.status === 'pending')
          : results,
        performance: {
          totalMethod,
          fromDate: performance.fromDate,
          endDate: performance.endDate,
          totalValidTrade: performance.totalValidTrade,
          totalInvalidTrade: performance.totalInvalidTrade,
          averageRewardRiskRatio: performance.averageRewardRiskRatio,
          probability: performance.probability,
          maxOpenPosition: performance.maxOpenPosition,
          maxStopPercent: performance.maxStopPercent,
          maxProfitPercent: performance.maxProfitPercent,
          maxConsecutiveLoss: performance.maxConsecutiveLoss,
          maxConsecutiveProfit: performance.maxConsecutiveProfit,
          maxHoldingPeriod: performance.maxHoldingPeriod,
          cumulativePercentage: performance.cumulativePercentage,
        },
      };
    } catch (error) {
      this.logger.error(
        'Failed to generate backtest method both',
        logDetail({
          class: 'BacktestMethodService',
          function: 'getBacktestMethodBoth',
          param,
          error,
        }),
      );
      throw new Error('Failed to generate backtest method');
    }
  }
}
