{"version": 3, "file": "historical-ingestion.service.js", "sourceRoot": "", "sources": ["../../src/services/historical-ingestion.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,2CAAkE;AAClE,+CAAuD;AACvD,qCAAiC;AACjC,6DAAqD;AACrD,uEAAgD;AAChD,uDAAmD;AACnD,uEAA8D;AAC9D,yEAAoE;AACpE,6DAAyD;AACzD,6DAAyD;AACzD,iEAA0D;AAC1D,2EAAkE;AAClE,mEAA8D;AAGvD,IAAM,0BAA0B,GAAhC,MAAM,0BAA0B;IAGlB;IACA;IACA;IACA;IACA;IACA;IAPnB,YAEmB,MAAc,EACd,iBAAoC,EACpC,mBAAwC,EACxC,cAA8B,EAC9B,sBAA8C,EAC9C,iBAAoC;QALpC,WAAM,GAAN,MAAM,CAAQ;QACd,sBAAiB,GAAjB,iBAAiB,CAAmB;QACpC,wBAAmB,GAAnB,mBAAmB,CAAqB;QACxC,mBAAc,GAAd,cAAc,CAAgB;QAC9B,2BAAsB,GAAtB,sBAAsB,CAAwB;QAC9C,sBAAiB,GAAjB,iBAAiB,CAAmB;IACnD,CAAC;IAEL,KAAK,CAAC,gBAAgB,CAAC,QAAiB;QACtC,MAAM,UAAU,GAAG,IAAA,wBAAc,EAAC,aAAa,CAAC,CAAC;QACjD,MAAM,cAAc,GAAG,IAAA,wBAAc,EAAC,iBAAiB,CAAC,CAAC;QACzD,MAAM,SAAS,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAA,wBAAc,EAAC,WAAW,CAAC,CAAC;QACtE,MAAM,kBAAkB,GAAG,IAAA,wBAAc,EAAC,oBAAoB,CAAC,CAAC;QAGhE,IAAI,UAAU,KAAK,QAAQ,EAAE,CAAC;YAC5B,IAAI,OAAO,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,CAAC;YACxD,OAAO,GAAG,cAAc,KAAK,OAAO;gBAClC,CAAC,CAAC,IAAA,gCAAY,EAAC,IAAA,wCAAe,EAAC,OAAO,CAAC,CAAC;gBACxC,CAAC,CAAC,IAAA,gCAAY,EAAC,OAAO,CAAC,CAAC;YAE1B,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC;QACrD,CAAC;QAGD,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;YAC7B,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,CAAC;YAC3E,IAAI,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAEtE,OAAO,GAAG,cAAc,KAAK,OAAO;gBAClC,CAAC,CAAC,IAAA,gCAAY,EAAC,IAAA,wCAAe,EAAC,OAAO,CAAC,CAAC;gBACxC,CAAC,CAAC,IAAA,gCAAY,EAAC,OAAO,CAAC,CAAC;YAC1B,MAAM,SAAS,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC;YACxH,MAAM,IAAI,CAAC,aAAa,CACtB,OAAO,EACP,GAAG,EAAE,CAAC,SAAS,CAChB,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa,CACzB,OAAiB,EACjB,YAA0C;QAE1C,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,KAAK,MAAM,QAAQ,IAAI,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC5C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,MAAM,IAAI,QAAQ,EAAE,CAAC,CAAC;gBACpD,MAAM,IAAI,CAAC,OAAO,CAChB,MAAM,EACN,QAAQ,CACT,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAAA,CAAC;IAEM,KAAK,CAAC,OAAO,CAAC,MAAc,EAAE,QAAgB;QACpD,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAA;YAC7B,MAAM,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;YAC3B,MAAM,YAAY,GAAG,IAAA,wBAAc,EAAC,eAAe,CAAC,CAAC;YACrD,MAAM,SAAS,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;YACtC,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;YAClC,MAAM,SAAS,GAAG,IAAA,oCAAa,EAAC,QAAQ,CAAC,CAAC;YAC1C,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC;gBAC5D,MAAM;aACP,CAAC,CAAC;YAEH,IAAI,CAAC,UAAU;gBAAE,OAAO;YAExB,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,EAAE,CAAA;YACvE,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAC/B,CAAC,OAAO,GAAG,UAAU,CAAC,GAAG,IAAA,oCAAa,EAAC,QAAQ,CAAC,CACjD,CAAC;YAEF,KAAK,IAAI,CAAC,GAAG,OAAO,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,IAAI,SAAS,GAAG,YAAY,EAAE,CAAC;gBACnE,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,CAAC,GAAG,SAAS,GAAG,YAAY,CAAC,CAAC;gBACzD,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC5B,MAAM,yBAAyB,GAC7B,MAAM,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAC/C,MAAM,EACN,QAAQ,EACR,SAAS,EACT,OAAO,CACR,CAAC;gBACJ,MAAM,oBAAoB,GACxB,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAC1C,MAAM,EACN,QAAQ,EACR,SAAS,EACT,OAAO,CACR,CAAC;gBACJ,IACE,yBAAyB,KAAK,YAAY;oBAC1C,oBAAoB,KAAK,YAAY,EACrC,CAAC;oBACD,MAAM,6BAA6B,GACjC,MAAM,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAC/C,MAAM,EACN,QAAQ,CACT,CAAC;oBACJ,MAAM,wBAAwB,GAC5B,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAC1C,MAAM,EACN,QAAQ,CACT,CAAC;oBAEJ,IACE,6BAA6B,KAAK,wBAAwB;wBAC1D,6BAA6B,IAAI,eAAe,EAChD,CAAC;wBACD,MAAM;oBACR,CAAC;oBACD,SAAS;gBACX,CAAC;gBACD,IACE,yBAAyB,KAAK,YAAY;oBAC1C,oBAAoB,KAAK,YAAY,EACrC,CAAC;oBACD,MAAM,oBAAoB,GACxB,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC;wBAC9C,MAAM;wBACN,QAAQ;wBACR,IAAI,EAAE,MAAM;wBACZ,KAAK,EAAE,YAAY;wBACnB,KAAK,EAAE,SAAS;wBAChB,GAAG,EAAE,OAAO;qBACb,CAAC,CAAC;oBACL,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,CAAC;oBACpE,SAAS;gBACX,CAAC;gBACD,IACE,yBAAyB,KAAK,YAAY;oBAC1C,oBAAoB,KAAK,YAAY,EACrC,CAAC;oBACD,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC;wBACjE,MAAM;wBACN,QAAQ;wBACR,IAAI,EAAE,MAAM;wBACZ,KAAK,EAAE,YAAY;wBACnB,KAAK,EAAE,SAAS;wBAChB,GAAG,EAAE,OAAO;qBACb,CAAC,CAAC;oBACH,MAAM,IAAI,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;oBACpE,SAAS;gBACX,CAAC;gBAED,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,oBAAoB,CAAC;oBACnE,MAAM;oBACN,QAAQ;oBACR,IAAI,EAAE,MAAM;oBACZ,KAAK,EAAE,YAAY;oBACnB,KAAK,EAAE,SAAS;oBAChB,GAAG,EAAE,OAAO;iBACb,CAAC,CAAC;gBAEH,MAAM,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;gBAC7D,MAAM,IAAI,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;gBAElE,IACE,oBAAoB,KAAK,aAAa,CAAC,MAAM;oBAC7C,yBAAyB,KAAK,aAAa,CAAC,MAAM,EAClD,CAAC;oBACD,MAAM;gBACR,CAAC;YACH,CAAC;YACD,OAAO;QACT,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,mCAAmC,EACnC,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,YAAY;gBACnB,QAAQ,EAAE,SAAS;gBACnB,KAAK,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE;gBAC3B,KAAK;aACN,CAAC,CACH,CAAC;YACF,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;CACF,CAAA;AAtLY,gEAA0B;qCAA1B,0BAA0B;IADtC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,eAAM,EAAC,sCAAuB,CAAC,CAAA;qCACP,gBAAM;QACK,sCAAiB;QACf,2CAAmB;QACxB,gCAAc;QACN,iDAAsB;QAC3B,sCAAiB;GAR5C,0BAA0B,CAsLtC"}