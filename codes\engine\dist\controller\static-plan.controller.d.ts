import { GetStaticPlanDto } from 'src/dto/get-static-plan.dto';
import { Plan } from 'src/interface/plan.interface';
import { StaticPlanService } from 'src/services/static-plan.service';
import { Logger } from 'winston';
export declare class StaticPlanController {
    private readonly staticPlanService;
    private readonly logger;
    constructor(staticPlanService: StaticPlanService, logger: Logger);
    getStaticPlan(body: GetStaticPlanDto): Promise<Plan[]>;
}
