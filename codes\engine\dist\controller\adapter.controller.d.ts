import { Logger } from 'winston';
import { AdapterService } from 'src/services/adapter.service';
import { GetHistoricalDto } from 'src/dto/get-historical.dto';
import { GetInstrumentDto } from 'src/dto/get-instrument.dto';
import { Historical } from 'src/interface/historical.interface';
import { Instrument } from 'src/interface/instrument.interface';
export declare class AdapterController {
    private readonly adapterService;
    private readonly logger;
    constructor(adapterService: AdapterService, logger: Logger);
    fetchBybitHistorical(body: GetHistoricalDto): Promise<Historical[]>;
    fetchBybitInstrument(body: GetInstrumentDto): Promise<Instrument[]>;
}
