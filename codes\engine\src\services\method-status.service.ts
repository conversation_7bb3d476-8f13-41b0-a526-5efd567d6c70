import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { MethodStatusEntity } from 'src/entity/method-status.entity';
import { MethodStatus } from 'src/interface/method-status.interface';
import { logDetail } from 'src/util/log-detail.util';
import { Repository, UpdateResult } from 'typeorm';
import { Logger } from 'winston';

@Injectable()
export class MethodStatusService {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    @InjectRepository(MethodStatusEntity)
    private readonly MethodsStatusRepository: Repository<MethodStatusEntity>,
  ) { }

  async getMethodStatusByMethodId(
    methodId: string,
  ): Promise<MethodStatusEntity | null> {
    try {
      const queryBuilder =
        this.MethodsStatusRepository.createQueryBuilder('method-status');

      return await queryBuilder
        .where('method-status.methodId = :methodId', { methodId })
        .getOne();
    } catch (err) {
      this.logger.error(
        'Failed to read method data',
        logDetail({
          class: 'MethodStatusService',
          function: 'getMethodStatusByMethodId',
          error: err,
        }),
      );
      throw new Error(`Failed to read method data`);
    }
  }

  async getMethodStatus(): Promise<MethodStatusEntity[] | null> {
    try {
      // Step 1: Ambil 1 simbol secara acak dari data yang isUpdated = false
      const symbolResult =
        await this.MethodsStatusRepository.createQueryBuilder('method-status')
          .select('method-status.symbol')
          .where('method-status.isUpdated = :isUpdated', { isUpdated: false })
          .groupBy('method-status.symbol')
          .orderBy('RANDOM()') // Randomize simbol
          .limit(1)
          .getRawOne();

      if (!symbolResult) {
        return null;
      }

      const selectedSymbol = symbolResult['method-status_symbol'];

      // Step 2: Ambil semua record dengan symbol tersebut
      const results = await this.MethodsStatusRepository.createQueryBuilder(
        'method-status',
      )
        .where('method-status.isUpdated = :isUpdated', { isUpdated: false })
        .andWhere('method-status.symbol = :symbol', { symbol: selectedSymbol })
        .getMany();

      return results;
    } catch (err) {
      this.logger.error(
        'Failed to read method data',
        logDetail({
          class: 'MethodStatusService',
          function: 'getMethodStatus',
          error: err,
        }),
      );
      throw new Error(`Failed to read method data`);
    }
  }

  async getAllMethodStatus(): Promise<MethodStatusEntity[]> {
    try {
      return await this.MethodsStatusRepository.find();
    } catch (err) {
      this.logger.error(
        'Failed to read method data',
        logDetail({
          class: 'MethodStatusService',
          function: 'getAllMethodStatus',
          error: err,
        }),
      );
      throw new Error(`Failed to read method data`);
    }
  }

  async insertMethodStatus(
    methodId: string,
    symbol: string,
    interval: string,
  ): Promise<void> {
    try {
      const newRecords: MethodStatus[] = [
        {
          methodId,
          interval,
          symbol,
          updatedAt: new Date(),
          isUpdated: false,
        },
      ];

      // Insert baru, skip kalau sudah ada
      await this.MethodsStatusRepository.createQueryBuilder()
        .insert()
        .into(MethodStatusEntity)
        .values(newRecords)
        .orIgnore()
        .execute();

      return;
    } catch (err) {
      this.logger.error(
        'Failed to sync method status data',
        logDetail({
          class: 'MethodStatusService',
          function: 'insertMethodStatus',
          error: err,
        }),
      );
      throw new Error('Failed to sync method status data');
    }
  }

  async updateMethodStatus(param: MethodStatus): Promise<UpdateResult> {
    try {
      return await this.MethodsStatusRepository.update(
        { methodId: param.methodId },
        { updatedAt: new Date(), isUpdated: true },
      );
    } catch (err) {
      this.logger.error(
        'Failed to update method data',
        logDetail({
          class: 'MethodStatusService',
          function: 'updateMethod',
          error: err,
          param,
        }),
      );
      throw new Error(`Failed to update method data`);
    }
  }

  async resetMethodStatus(interval: string): Promise<UpdateResult> {
    try {
      return await this.MethodsStatusRepository.update(
        { isUpdated: true, interval },
        { updatedAt: new Date(), isUpdated: false },
      );
    } catch (err) {
      this.logger.error(
        'Failed to reset method data',
        logDetail({
          class: 'MethodStatusService',
          function: 'resetMethod',
          error: err,
        }),
      );
      throw new Error(`Failed to reset method data`);
    }
  }

  async deleteMethodStatus(methodId: string): Promise<void> {
    try {
      await this.MethodsStatusRepository.delete({ methodId });
    } catch (err) {
      this.logger.error(
        'Failed to delete method data',
        logDetail({
          class: 'MethodStatusService',
          function: 'deleteMethod',
          error: err,
        }),
      );
      throw new Error(`Failed to delete method data`);
    }
  }
}
