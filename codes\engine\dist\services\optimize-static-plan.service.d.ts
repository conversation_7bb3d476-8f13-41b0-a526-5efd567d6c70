import { Logger } from 'winston';
import { BacktestService } from './backtest.service';
import { MethodResult } from 'src/interface/method-result.interface';
import { GetStaticPlanDto } from 'src/dto/get-static-plan.dto';
import { Pattern } from 'src/interface/pattern.interface';
import { Historical } from 'src/interface/historical.interface';
import { PatternService } from './pattern.service';
import { StaticPlanService } from './static-plan.service';
import { Plan } from 'src/interface/plan.interface';
import { OptimizedStaticPlan } from 'src/interface/optimized-static-plan.interface';
import { Instrument } from 'src/interface/instrument.interface';
import { InstrumentService } from './instrument.service';
import { HistoricalCacheService } from './historical-cache.service';
export declare class OptimizeStaticPlanService {
    private readonly logger;
    private readonly backtestService;
    private readonly historicalCacheService;
    private readonly patternService;
    private readonly staticPlanService;
    private readonly instrumentService;
    constructor(logger: Logger, backtestService: BacktestService, historicalCacheService: HistoricalCacheService, patternService: PatternService, staticPlanService: StaticPlanService, instrumentService: InstrumentService);
    optimize(param: GetStaticPlanDto, historical?: Historical[], historicalExecution?: Historical[], patterns?: Pattern[], instrument?: Instrument[], plan?: Plan[], results?: MethodResult[]): Promise<OptimizedStaticPlan>;
    getOptimizedStaticPlan(param: GetStaticPlanDto, historical?: Historical[], historicalExecution?: Historical[], patterns?: Pattern[], instrument?: Instrument[], plan?: Plan[], results?: MethodResult[]): Promise<Plan[]>;
}
