import { Historical } from '../interface/historical.interface';
export declare class CandlestickService {
    bodyRange(candle: Historical): number;
    topBodyPrice(candle: Historical): number;
    bottomBodyPrice(candle: Historical): number;
    halfBodyPrice(candle: Historical): number;
    boneRange(candle: Historical): number;
    topBonePrice(candle: Historical): number;
    bottomBonePrice(candle: Historical): number;
    halfBonePrice(candle: Historical): number;
    topDiv3bonePrice(candle: Historical): number;
    bottomDiv3bonePrice(candle: Historical): number;
    shadowTopRange(candle: Historical): number;
    shadowBottomRange(candle: Historical): number;
    isGapBetweenCandles(params: {
        firstCandle: Historical;
        secondCandle: Historical;
    }): boolean;
    isTrendForCandlestickPattern(params: {
        trend: string;
        candles: Historical[];
        sampleCandleLength: number;
    }): boolean;
    isBullish(candle: Historical): boolean;
    isBearish(candle: Historical): boolean;
    isLongBody<PERSON>reen(candle: Historical): boolean;
    isShortBodyGreen(candle: Historical): boolean;
    isLongBodyRed(candle: Historical): boolean;
    isShortBodyRed(candle: Historical): boolean;
    isWithoutBody(candle: Historical): boolean;
}
