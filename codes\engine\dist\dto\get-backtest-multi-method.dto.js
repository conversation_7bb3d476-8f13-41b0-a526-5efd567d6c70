"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetBacktestMultiMethodDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class GetBacktestMultiMethodDto {
    minProbability;
    methodLimit;
    pendingResultOnly;
}
exports.GetBacktestMultiMethodDto = GetBacktestMultiMethodDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Minimum Probability',
        example: 70,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], GetBacktestMultiMethodDto.prototype, "minProbability", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Limit of method data to return (e.g., 100)',
        example: 1000,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], GetBacktestMultiMethodDto.prototype, "methodLimit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Only return pending results',
        example: false,
    }),
    (0, class_validator_1.IsBoolean)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], GetBacktestMultiMethodDto.prototype, "pendingResultOnly", void 0);
//# sourceMappingURL=get-backtest-multi-method.dto.js.map