{"version": 3, "file": "get-result.dto.js", "sourceRoot": "", "sources": ["../../src/dto/get-result.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,6CAA8C;AAC9C,qDAAyE;AACzE,yDAAyC;AAEzC,MAAa,YAAY;IAOvB,EAAE,CAAS;IAUX,IAAI,CAAO;IAQX,QAAQ,CAAS;IAQjB,MAAM,CAAS;IAQf,SAAS,CAAS;IAOlB,mBAAmB,CAAS;IAO5B,WAAW,CAAS;IAOpB,aAAa,CAAS;IAOtB,cAAc,CAAS;IAOvB,KAAK,CAAS;IAOd,QAAQ,CAAS;IAOjB,UAAU,CAAS;IAOnB,WAAW,CAAS;IAOpB,aAAa,CAAS;IAUtB,UAAU,CAAO;IAQjB,WAAW,CAAS;IAQpB,OAAO,CAAS;CACjB;AAnID,oCAmIC;AA5HC;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,uBAAuB;QACpC,OAAO,EAAE,oCAAoC;KAC9C,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;wCACF;AAUX;IARC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,kBAAkB;QAC/B,OAAO,EAAE,0BAA0B;QACnC,IAAI,EAAE,MAAM;QACZ,MAAM,EAAE,WAAW;KACpB,CAAC;IACD,IAAA,wBAAM,GAAE;IACR,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,IAAI,CAAC;8BACX,IAAI;0CAAC;AAQX;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,eAAe;QAC5B,OAAO,EAAE,GAAG;KACb,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;8CACI;AAQjB;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,gBAAgB;QAC7B,OAAO,EAAE,SAAS;KACnB,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;4CACE;AAQf;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,kCAAkC;QAC/C,OAAO,EAAE,MAAM;KAChB,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;+CACK;AAOlB;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,wBAAwB;QACrC,OAAO,EAAE,CAAC,GAAG;KACd,CAAC;IACD,IAAA,0BAAQ,GAAE;;yDACiB;AAO5B;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,wBAAwB;QACrC,OAAO,EAAE,CAAC,GAAG;KACd,CAAC;IACD,IAAA,0BAAQ,GAAE;;iDACS;AAOpB;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,0BAA0B;QACvC,OAAO,EAAE,GAAG;KACb,CAAC;IACD,IAAA,0BAAQ,GAAE;;mDACW;AAOtB;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,qCAAqC;QAClD,OAAO,EAAE,CAAC;KACX,CAAC;IACD,IAAA,0BAAQ,GAAE;;oDACY;AAOvB;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,aAAa;QAC1B,OAAO,EAAE,OAAO;KACjB,CAAC;IACD,IAAA,0BAAQ,GAAE;;2CACG;AAOd;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,iBAAiB;QAC9B,OAAO,EAAE,OAAO;KACjB,CAAC;IACD,IAAA,0BAAQ,GAAE;;8CACM;AAOjB;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,mBAAmB;QAChC,OAAO,EAAE,OAAO;KACjB,CAAC;IACD,IAAA,0BAAQ,GAAE;;gDACQ;AAOnB;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,cAAc;QAC3B,OAAO,EAAE,CAAC,GAAG;KACd,CAAC;IACD,IAAA,0BAAQ,GAAE;;iDACS;AAOpB;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,gBAAgB;QAC7B,OAAO,EAAE,GAAG;KACb,CAAC;IACD,IAAA,0BAAQ,GAAE;;mDACW;AAUtB;IARC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,0BAA0B;QACvC,OAAO,EAAE,0BAA0B;QACnC,IAAI,EAAE,MAAM;QACZ,MAAM,EAAE,WAAW;KACpB,CAAC;IACD,IAAA,wBAAM,GAAE;IACR,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,IAAI,CAAC;8BACL,IAAI;gDAAC;AAQjB;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,kCAAkC;QAC/C,OAAO,EAAE,aAAa;KACvB,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;iDACO;AAQpB;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,wCAAwC;QACrD,OAAO,EAAE,QAAQ;KAClB,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,4BAAU,GAAE;;6CACG"}