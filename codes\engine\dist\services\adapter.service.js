"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdapterService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const axios_1 = __importDefault(require("axios"));
const configurations_1 = __importDefault(require("../configurations"));
const log_detail_util_1 = require("../util/log-detail.util");
let AdapterService = class AdapterService {
    logger;
    constructor(logger) {
        this.logger = logger;
    }
    async fetchBybitHistorical(param) {
        const startTimestamp = param.start.getTime();
        const endTimestamp = param.end.getTime();
        const url = `${(0, configurations_1.default)('BYBIT_API_HOST')}/v5/market/kline` +
            `?category=linear&symbol=${param.symbol}` +
            `&interval=${param.interval}&start=${startTimestamp}&end=${endTimestamp}&limit=${param.limit}`;
        try {
            const response = await axios_1.default.get(url, { timeout: 5000 });
            const rawData = response.data?.result?.list;
            if (!Array.isArray(rawData)) {
                this.logger.warn('Unexpected response format from Bybit API', (0, log_detail_util_1.logDetail)({
                    class: 'AppService',
                    function: 'fetchBybitHistorical',
                    url,
                    param,
                }));
                return [];
            }
            const historicals = rawData
                .map((item) => ({
                symbol: param.symbol,
                date: new Date(Number(item[0])),
                open: parseFloat(item[1]),
                high: parseFloat(item[2]),
                low: parseFloat(item[3]),
                close: parseFloat(item[4]),
                volume: parseFloat(item[5]),
                interval: param.interval,
            }))
                .sort((a, b) => {
                if (param.sort === 'DESC') {
                    return b.date.getTime() - a.date.getTime();
                }
                else {
                    return a.date.getTime() - b.date.getTime();
                }
            });
            return historicals;
        }
        catch (err) {
            const error = err;
            this.logger.error(`Failed to fetch historical data`, (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'fetchBybitHistorical',
                url,
                param,
                error: error.toJSON ? error.toJSON() : error,
            }));
            return await this.fetchBybitHistorical(param);
        }
    }
    async fetchBybitInstrument(param) {
        let url = '';
        if (!param) {
            url =
                `${(0, configurations_1.default)('BYBIT_API_HOST')}/v5/market/instruments-info` +
                    `?category=linear` +
                    `&limit=${(0, configurations_1.default)('DEFAULT_LIMIT')}`;
        }
        else {
            url =
                `${(0, configurations_1.default)('BYBIT_API_HOST')}/v5/market/instruments-info` +
                    `?category=linear&symbol=${param.symbol}` +
                    `&limit=${(0, configurations_1.default)('DEFAULT_LIMIT')}`;
        }
        try {
            const response = await axios_1.default.get(url, { timeout: 5000 });
            const rawData = response.data?.result?.list;
            if (!Array.isArray(rawData)) {
                this.logger.warn('Unexpected response format from Bybit API', (0, log_detail_util_1.logDetail)({
                    class: 'AppService',
                    function: 'fetchBybitInstrument',
                    url,
                    param,
                }));
                return [];
            }
            const parsedData = rawData.map((item) => ({
                symbol: String(item.symbol ?? ''),
                launchTime: item.launchTime === undefined ? 0 : Number(item.launchTime),
                listedTime: item.listedTime === undefined ? 0 : Number(item.listedTime),
                priceScale: item.priceScale === undefined ? 0 : Number(item.priceScale),
                leverageFilter: {
                    minLeverage: Number(item.leverageFilter?.minLeverage ?? 0),
                    maxLeverage: Number(item.leverageFilter?.maxLeverage ?? 0),
                    leverageStep: Number(item.leverageFilter?.leverageStep ?? 0),
                },
                priceFilter: {
                    minPrice: Number(item.priceFilter?.minPrice ?? 0),
                    maxPrice: Number(item.priceFilter?.maxPrice ?? 0),
                    tickSize: Number(item.priceFilter?.tickSize ?? 0),
                },
                lotSizeFilter: {
                    maxOrderQty: Number(item.lotSizeFilter?.maxOrderQty ?? 0),
                    minOrderQty: Number(item.lotSizeFilter?.minOrderQty ?? 0),
                    qtyStep: Number(item.lotSizeFilter?.qtyStep ?? 0),
                    postOnlyMaxOrderQty: Number(item.lotSizeFilter?.postOnlyMaxOrderQty ?? 0),
                    maxMktOrderQty: Number(item.lotSizeFilter?.maxMktOrderQty ?? 0),
                    minNotionalValue: Number(item.lotSizeFilter?.minNotionalValue ?? 0),
                },
                fundingInterval: item.fundingInterval === null || item.fundingInterval === undefined
                    ? 0
                    : Number(item.fundingInterval),
                upperFundingRate: Number(item.upperFundingRate ?? 0),
                lowerFundingRate: Number(item.lowerFundingRate ?? 0),
                auctionFeeInfo: {
                    auctionFeeRate: (0, configurations_1.default)('BYBIT_AUCTION_FEE_RATE'),
                    takerFeeRate: (0, configurations_1.default)('BYBIT_TAKER_FEE_RATE'),
                    makerFeeRate: (0, configurations_1.default)('BYBIT_MAKER_FEE_RATE'),
                },
            }));
            return parsedData;
        }
        catch (err) {
            const error = err;
            this.logger.error(`Failed to fetch instrument data`, (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'fetchBybitInstrument',
                url,
                param,
                error: error.toJSON ? error.toJSON() : error,
            }));
            return await this.fetchBybitInstrument(param);
        }
    }
};
exports.AdapterService = AdapterService;
exports.AdapterService = AdapterService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [winston_1.Logger])
], AdapterService);
//# sourceMappingURL=adapter.service.js.map