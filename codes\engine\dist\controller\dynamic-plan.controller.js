"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DynamicPlanController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const dynamic_plan_service_1 = require("../services/dynamic-plan.service");
const get_dynamic_plan_dto_1 = require("../dto/get-dynamic-plan.dto");
const log_detail_util_1 = require("../util/log-detail.util");
let DynamicPlanController = class DynamicPlanController {
    appService;
    logger;
    constructor(appService, logger) {
        this.appService = appService;
        this.logger = logger;
    }
    async getDynamicPlan(body) {
        try {
            body.start = new Date(body.start);
            body.end = new Date(body.end);
            const result = await this.appService.getPlan(body);
            return result ?? [];
        }
        catch (error) {
            const message = error?.message || 'Unknown error';
            this.logger.error('Dynamic plan data fetch failed', (0, log_detail_util_1.logDetail)({
                class: 'AppController',
                function: 'getDynamicPlan',
                body,
                error: error.stack || message,
            }));
            throw new common_1.HttpException(message, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.DynamicPlanController = DynamicPlanController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get Dynamic Plan' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of Dynamic Plan Data',
        isArray: true,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_dynamic_plan_dto_1.GetDynamicPlanDto]),
    __metadata("design:returntype", Promise)
], DynamicPlanController.prototype, "getDynamicPlan", null);
exports.DynamicPlanController = DynamicPlanController = __decorate([
    (0, swagger_1.ApiTags)('Dynamic Plan'),
    (0, common_1.Controller)('dynamic-plan'),
    __param(1, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [dynamic_plan_service_1.DynamicPlanService,
        winston_1.Logger])
], DynamicPlanController);
//# sourceMappingURL=dynamic-plan.controller.js.map