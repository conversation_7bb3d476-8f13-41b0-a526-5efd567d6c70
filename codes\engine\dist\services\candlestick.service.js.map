{"version": 3, "file": "candlestick.service.js", "sourceRoot": "", "sources": ["../../src/services/candlestick.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAE5C,uEAA+C;AAGxC,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;IAC7B,SAAS,CAAC,MAAkB;QAC1B,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IAC9C,CAAC;IAED,YAAY,CAAC,MAAkB;QAC7B,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;IAC7C,CAAC;IAED,eAAe,CAAC,MAAkB;QAChC,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;IAC7C,CAAC;IAED,aAAa,CAAC,MAAkB;QAC9B,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IACnE,CAAC;IAED,SAAS,CAAC,MAAkB;QAC1B,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;IAC5C,CAAC;IAED,YAAY,CAAC,MAAkB;QAC7B,OAAO,MAAM,CAAC,IAAI,CAAC;IACrB,CAAC;IAED,eAAe,CAAC,MAAkB;QAChC,OAAO,MAAM,CAAC,GAAG,CAAC;IACpB,CAAC;IAED,aAAa,CAAC,MAAkB;QAC9B,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IACnE,CAAC;IAED,gBAAgB,CAAC,MAAkB;QACjC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IACzE,CAAC;IAED,mBAAmB,CAAC,MAAkB;QACpC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IACzE,CAAC;IAED,cAAc,CAAC,MAAkB;QAC/B,OAAO,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI;YAC/B,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC;YACtC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC;IAED,iBAAiB,CAAC,MAAkB;QAClC,OAAO,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI;YAC/B,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC;YACpC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IAC1C,CAAC;IAED,mBAAmB,CAAC,MAGnB;QACC,OAAO,CACL,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,YAAY,CAAC;YACpC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,WAAW,CAAC;YAC1C,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,YAAY,CAAC;gBACvC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,WAAW,CAAC,CACxC,CAAC;IACJ,CAAC;IAED,4BAA4B,CAAC,MAI5B;QACC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,IAAA,wBAAc,EAAC,6BAA6B,CAAC,EAAE,CAAC;YAC1E,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO;aAC/B,KAAK,CACJ,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,IAAA,wBAAc,EAAC,6BAA6B,CAAC,EACrE,MAAM,CAAC,OAAO,CAAC,MAAM,CACtB;aACA,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;QAE/C,MAAM,aAAa,GACjB,WAAW,CAAC,MAAM,CAChB,CAAC,WAAW,EAAE,YAAY,EAAE,EAAE,CAAC,WAAW,GAAG,YAAY,EACzD,CAAC,CACF,GAAG,WAAW,CAAC,MAAM,CAAC;QAEzB,MAAM,KAAK,GACT,aAAa,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG;YAC3D,CAAC,CAAC,IAAI;YACN,CAAC,CAAC,aAAa,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI;gBAC9D,CAAC,CAAC,MAAM;gBACR,CAAC,CAAC,SAAS,CAAC;QAElB,OAAO,MAAM,CAAC,KAAK,KAAK,KAAK,CAAC;IAChC,CAAC;IAED,SAAS,CAAC,MAAkB;QAC1B,OAAO,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC;IACpC,CAAC;IAED,SAAS,CAAC,MAAkB;QAC1B,OAAO,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC;IACpC,CAAC;IAED,eAAe,CAAC,MAAkB;QAChC,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACzC,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACjD,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACzC,OAAO,CACL,SAAS,IAAI,aAAa;YAC1B,SAAS,GAAG,IAAI,GAAG,SAAS;YAC5B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CACvB,CAAC;IACJ,CAAC;IAED,gBAAgB,CAAC,MAAkB;QACjC,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACzC,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QACjD,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAEzC,OAAO,CACL,SAAS,GAAG,CAAC;YACb,SAAS,GAAG,aAAa;YACzB,SAAS,GAAG,IAAI,GAAG,SAAS;YAC5B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CACvB,CAAC;IACJ,CAAC;IAED,aAAa,CAAC,MAAkB;QAC9B,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACzC,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACjD,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACzC,OAAO,CACL,SAAS,IAAI,aAAa;YAC1B,SAAS,GAAG,IAAI,GAAG,SAAS;YAC5B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CACvB,CAAC;IACJ,CAAC;IAED,cAAc,CAAC,MAAkB;QAC/B,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACzC,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QACjD,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAEzC,OAAO,CACL,SAAS,GAAG,CAAC;YACb,SAAS,GAAG,aAAa;YACzB,SAAS,GAAG,IAAI,GAAG,SAAS;YAC5B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CACvB,CAAC;IACJ,CAAC;IAED,aAAa,CAAC,MAAkB;QAC9B,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACzC,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAEzC,OAAO,SAAS,IAAI,IAAI,GAAG,SAAS,CAAC;IACvC,CAAC;CACF,CAAA;AA/JY,gDAAkB;6BAAlB,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;GACA,kBAAkB,CA+J9B"}