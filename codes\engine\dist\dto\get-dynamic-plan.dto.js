"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetDynamicPlanDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const get_static_plan_dto_1 = require("./get-static-plan.dto");
class GetDynamicPlanDto extends get_static_plan_dto_1.GetStaticPlanDto {
    lookbackPeriod;
}
exports.GetDynamicPlanDto = GetDynamicPlanDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Lookback Period',
        example: 100,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], GetDynamicPlanDto.prototype, "lookbackPeriod", void 0);
//# sourceMappingURL=get-dynamic-plan.dto.js.map