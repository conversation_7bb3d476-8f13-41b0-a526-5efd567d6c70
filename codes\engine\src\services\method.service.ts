import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { logDetail } from 'src/util/log-detail.util';
import { InsertResult, Repository, UpdateResult } from 'typeorm';
import { Logger } from 'winston';
import { MethodParamEntity } from 'src/entity/method-param.entity';
import { MethodResultEntity } from 'src/entity/method-result.entity';
import { MethodPerformanceEntity } from 'src/entity/method-performance.entity';
import { GetBacktestMethodDto } from 'src/dto/get-backtest-method.dto';
import { GetMethodDto } from 'src/dto/get-method.dto';
import { MethodResult } from 'src/interface/method-result.interface';
import { BacktestPerformance } from 'src/interface/backtest-performance.interface';
import { GetBacktestMultiMethodDto } from 'src/dto/get-backtest-multi-method.dto';

@Injectable()
export class MethodService {
  constructor(
    @InjectRepository(MethodParamEntity)
    private readonly MethodParamsRepository: Repository<MethodParamEntity>,
    @InjectRepository(MethodResultEntity)
    private readonly MethodResultRepository: Repository<MethodResultEntity>,
    @InjectRepository(MethodPerformanceEntity)
    private readonly MethodPerformanceRepository: Repository<MethodPerformanceEntity>,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  async deleteMethodResultNotIn(methodIds: string[]): Promise<void> {
    try {
      await this.MethodResultRepository.manager.transaction(
        async (transactionalEntityManager) => {
          await transactionalEntityManager
            .createQueryBuilder()
            .delete()
            .from(MethodResultEntity)
            .where('methodId NOT IN (:...methodIds)', { methodIds })
            .execute();
        },
      );
    } catch (err) {
      this.logger.error(
        'Failed to delete method result data',
        logDetail({
          class: 'MethodService',
          function: 'deleteMethodResultNotIn',
          error: err,
        }),
      );
      throw new Error('Failed to delete method result data');
    }
  }

  async deleteMethodPerformanceNotIn(methodIds: string[]): Promise<void> {
    try {
      await this.MethodPerformanceRepository.manager.transaction(
        async (transactionalEntityManager) => {
          await transactionalEntityManager
            .createQueryBuilder()
            .delete()
            .from(MethodPerformanceEntity)
            .where('methodId NOT IN (:...methodIds)', { methodIds })
            .execute();
        },
      );
    } catch (err) {
      this.logger.error(
        'Failed to delete method performance data',
        logDetail({
          class: 'MethodService',
          function: 'deleteMethodPerformanceNotIn',
          error: err,
        }),
      );
      throw new Error('Failed to delete method performance data');
    }
  }

  async getMethodIds(): Promise<MethodParamEntity[]> {
    try {
      const queryBuilder =
        this.MethodParamsRepository.createQueryBuilder('method-param');
      return await queryBuilder.select('method-param.methodId').getMany();
    } catch (err) {
      this.logger.error(
        'Failed to read method-param data',
        logDetail({
          class: 'MethodService',
          function: 'getMethodIds',
          error: err,
        }),
      );
      throw new Error(`Failed to read method-param data`);
    }
  }

  async insertMethodParam(param: GetBacktestMethodDto): Promise<InsertResult> {
    try {
      const result = await this.MethodParamsRepository.upsert(
        param,
        ['methodId'], // assuming methodId is the conflict target
      );

      return result;
    } catch (err) {
      this.logger.error(
        'Failed to insert method-param data',
        logDetail({
          class: 'MethodParamsService',
          function: 'insertMethodParam',
          error: err,
          param,
        }),
      );
      throw new Error('Failed to insert method-param data');
    }
  }

  async insertMethodResult(param: MethodResult[]): Promise<InsertResult> {
    try {
      return await this.MethodResultRepository.upsert(
        param,
        ['methodId', 'date'], // conflict target
      );
    } catch (err) {
      this.logger.error(
        'Failed to insert method-result data',
        logDetail({
          class: 'AppService',
          function: 'insertMethodResult',
          error: err,
          param,
        }),
      );
      throw new Error('Failed to insert method-result data');
    }
  }
  async insertMethodPerformance(
    param: BacktestPerformance,
  ): Promise<InsertResult | UpdateResult> {
    try {
      // 1. Normalisasi data
      param.methodId = param.methodId.trim();

      // 2. Coba update terlebih dahulu
      const updateResult = await this.MethodPerformanceRepository.update(
        {
          methodId: param.methodId,
        },
        {
          fromDate: param.fromDate,
          endDate: param.endDate,
          totalValidTrade: param.totalValidTrade,
          totalInvalidTrade: param.totalInvalidTrade,
          probability: param.probability,
          averageRewardRiskRatio: param.averageRewardRiskRatio,
          maxOpenPosition: param.maxOpenPosition,
          maxConsecutiveLoss: param.maxConsecutiveLoss,
          maxConsecutiveProfit: param.maxConsecutiveProfit,
          cumulativePercentage: param.cumulativePercentage,
          maxHoldingPeriod: param.maxHoldingPeriod,
        },
      );

      // 3. Jika tidak ada yang diupdate, lakukan insert
      if (updateResult.affected === 0) {
        return await this.MethodPerformanceRepository.insert(param);
      }

      return updateResult;
    } catch (err) {
      // 4. Handle race condition
      if (err.code === '23505') {
        // Duplicate key
        return await this.MethodPerformanceRepository.update(
          {
            methodId: param.methodId,
            fromDate: param.fromDate,
            endDate: param.endDate,
          },
          param,
        );
      }
      throw err;
    }
  }

  async getParam(param: GetMethodDto): Promise<MethodParamEntity[]> {
    try {
      const queryBuilder =
        this.MethodParamsRepository.createQueryBuilder('method-param');

      // Add conditions based on provided parameters
      if (param.methodId) {
        queryBuilder.andWhere('method-param.methodId = :methodId', {
          methodId: param.methodId,
        });
      }

      return await queryBuilder.getMany();
    } catch (err) {
      this.logger.error(
        'Failed to read method-param data',
        logDetail({
          class: 'AppService',
          function: 'read',
          error: err,
          param,
        }),
      );
      throw new Error(`Failed to read method-param data`);
    }
  }

  async getResult(param: GetMethodDto): Promise<MethodResultEntity[]> {
    try {
      const queryBuilder =
        this.MethodResultRepository.createQueryBuilder('method-result');

      // Add conditions based on provided parameters
      if (param.methodId) {
        queryBuilder.andWhere('method-result.methodId = :methodId', {
          methodId: param.methodId,
        });
      }

      return await queryBuilder.getMany();
    } catch (err) {
      this.logger.error(
        'Failed to read method-result data',
        logDetail({
          class: 'AppService',
          function: 'getResult',
          error: err,
          param,
        }),
      );
      throw new Error(`Failed to read method-result data`);
    }
  }

  async getPerformance(
    param: GetMethodDto,
  ): Promise<MethodPerformanceEntity[]> {
    try {
      const queryBuilder =
        this.MethodPerformanceRepository.createQueryBuilder(
          'method-performance',
        );

      // Add conditions based on provided parameters
      if (param.methodId) {
        queryBuilder.andWhere('method-performance.methodId = :methodId', {
          methodId: param.methodId,
        });
      }

      return await queryBuilder.getMany();
    } catch (err) {
      this.logger.error(
        'Failed to read method-performance data',
        logDetail({
          class: 'AppService',
          function: 'getPerformance',
          error: err,
          param,
        }),
      );
      throw new Error(`Failed to read method-performance data`);
    }
  }

  private async getMethodsByPerformance(
    param: GetBacktestMultiMethodDto,
  ): Promise<MethodPerformanceEntity[]> {
    const { minProbability, methodLimit } = param;

    try {
      const rawResults = await this.MethodPerformanceRepository.query(
        `
          SELECT "methodId", "probability"
          FROM (
            SELECT   
              mp2."methodId", mp."probability",
              ROW_NUMBER() OVER (
                PARTITION BY mp2."symbol", mp2."interval", mp2."orderType", mp2."pattern"
                ORDER BY mp."probability" DESC
              ) AS rn
            FROM public."method-performance" mp  
            JOIN public."method-param" mp2   
              ON mp."methodId" = mp2."methodId"  
            WHERE mp."probability" > $1
          ) AS sub
          WHERE rn = 1
          ORDER BY "probability" DESC
          LIMIT $2;
          `,
        [minProbability ?? 70, methodLimit ?? 1000],
      );

      return rawResults.map((item: any) => item.methodId);
    } catch (err) {
      this.logger.error(
        'Failed to read method-performance data',
        logDetail({
          class: 'AppService',
          function: 'getMethodsByPerformance',
          error: err,
          param,
        }),
      );
      throw new Error('Failed to read method-performance data');
    }
  }

  async getMultiMethodResult(param: GetBacktestMultiMethodDto) {
    try {
      const methodIds = await this.getMethodsByPerformance(param);

      if (methodIds.length === 0) {
        return [];
      }

      const queryBuilder =
        this.MethodResultRepository.createQueryBuilder('method-result');

      queryBuilder.where('method-result.methodId IN (:...methodIds)', {
        methodIds,
      });

      queryBuilder.orderBy('method-result.date', 'ASC');

      const result = await queryBuilder.getMany();
      return result.map((item: any) => ({
        ...item,
        date: new Date(item.date),
        openDate: item.openDate ? new Date(item.openDate) : null,
        closedDate: item.closedDate ? new Date(item.closedDate) : null,
        expiryDate: new Date(item.expiryDate),
      }));
    } catch (err) {
      this.logger.error(
        'Failed to read method-result data',
        logDetail({
          class: 'AppService',
          function: 'getResultsByMethods',
          error: err,
          param,
        }),
      );
      throw new Error('Failed to read method-result data');
    }
  }

  async deleteMethodResult(methodId: string): Promise<void> {
    try {
      await this.MethodResultRepository.delete({ methodId });
    } catch (err) {
      this.logger.error(
        'Failed to delete method-result data',
        logDetail({
          class: 'MethodService',
          function: 'deleteMethodResult',
          error: err,
        }),
      );
      throw new Error(`Failed to delete method-result data`);
    }
  }

  async deleteMethodPerformance(methodId: string): Promise<void> {
    try {
      await this.MethodPerformanceRepository.delete({ methodId });
    } catch (err) {
      this.logger.error(
        'Failed to delete method-performance data',
        logDetail({
          class: 'MethodService',
          function: 'deleteMethodPerformance',
          error: err,
        }),
      );
      throw new Error(`Failed to delete method-performance data`);
    }
  }

  async deleteMethodParam(methodId: string): Promise<void> {
    try {
      await this.MethodParamsRepository.delete({ methodId });
    } catch (err) {
      this.logger.error(
        'Failed to delete method-param data',
        logDetail({
          class: 'MethodService',
          function: 'deleteMethodParam',
          error: err,
        }),
      );
      throw new Error(`Failed to delete method-param data`);
    }
  }
}
