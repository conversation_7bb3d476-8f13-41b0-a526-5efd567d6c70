"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BacktestMultiMethodService = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const backtest_service_1 = require("./backtest.service");
const log_detail_util_1 = require("../util/log-detail.util");
const method_service_1 = require("./method.service");
let BacktestMultiMethodService = class BacktestMultiMethodService {
    logger;
    backtestService;
    methodService;
    constructor(logger, backtestService, methodService) {
        this.logger = logger;
        this.backtestService = backtestService;
        this.methodService = methodService;
    }
    async getBacktestMultiMethodResult(param) {
        try {
            const results = await this.methodService.getMultiMethodResult(param);
            return param.pendingResultOnly
                ? results.filter((item) => item.status === 'pending')
                : results;
        }
        catch (error) {
            this.logger.error('Failed to generate backtest method result', (0, log_detail_util_1.logDetail)({
                class: 'BacktestMethodService',
                function: 'getBacktestMethodResult',
                param,
                error,
            }));
            throw new Error('Failed to generate backtest method');
        }
    }
    async getBacktestMultiMethodPerformance(param) {
        try {
            const results = await this.methodService.getMultiMethodResult(param);
            const totalMethod = [...new Set(results.map((item) => item.methodId))]
                .length;
            const performance = await this.backtestService.getPerformance(results);
            return {
                totalMethod,
                fromDate: performance.fromDate,
                endDate: performance.endDate,
                totalValidTrade: performance.totalValidTrade,
                totalInvalidTrade: performance.totalInvalidTrade,
                averageRewardRiskRatio: performance.averageRewardRiskRatio,
                probability: performance.probability,
                maxOpenPosition: performance.maxOpenPosition,
                maxStopPercent: performance.maxStopPercent,
                maxProfitPercent: performance.maxProfitPercent,
                maxConsecutiveLoss: performance.maxConsecutiveLoss,
                maxConsecutiveProfit: performance.maxConsecutiveProfit,
                maxHoldingPeriod: performance.maxHoldingPeriod,
                cumulativePercentage: performance.cumulativePercentage,
            };
        }
        catch (error) {
            this.logger.error('Failed to generate backtest method performance', (0, log_detail_util_1.logDetail)({
                class: 'BacktestMethodService',
                function: 'getBacktestMethodPerformance',
                param,
                error,
            }));
            throw new Error('Failed to generate backtest method');
        }
    }
    async getBacktestMutilMethodBoth(param) {
        try {
            const results = await this.methodService.getMultiMethodResult(param);
            const totalMethod = [...new Set(results.map((item) => item.methodId))]
                .length;
            const performance = await this.backtestService.getPerformance(results);
            return {
                result: param.pendingResultOnly
                    ? results.filter((item) => item.status === 'pending')
                    : results,
                performance: {
                    totalMethod,
                    fromDate: performance.fromDate,
                    endDate: performance.endDate,
                    totalValidTrade: performance.totalValidTrade,
                    totalInvalidTrade: performance.totalInvalidTrade,
                    averageRewardRiskRatio: performance.averageRewardRiskRatio,
                    probability: performance.probability,
                    maxOpenPosition: performance.maxOpenPosition,
                    maxStopPercent: performance.maxStopPercent,
                    maxProfitPercent: performance.maxProfitPercent,
                    maxConsecutiveLoss: performance.maxConsecutiveLoss,
                    maxConsecutiveProfit: performance.maxConsecutiveProfit,
                    maxHoldingPeriod: performance.maxHoldingPeriod,
                    cumulativePercentage: performance.cumulativePercentage,
                },
            };
        }
        catch (error) {
            this.logger.error('Failed to generate backtest method both', (0, log_detail_util_1.logDetail)({
                class: 'BacktestMethodService',
                function: 'getBacktestMethodBoth',
                param,
                error,
            }));
            throw new Error('Failed to generate backtest method');
        }
    }
};
exports.BacktestMultiMethodService = BacktestMultiMethodService;
exports.BacktestMultiMethodService = BacktestMultiMethodService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [winston_1.Logger,
        backtest_service_1.BacktestService,
        method_service_1.MethodService])
], BacktestMultiMethodService);
//# sourceMappingURL=backtest-multi-method.service.js.map