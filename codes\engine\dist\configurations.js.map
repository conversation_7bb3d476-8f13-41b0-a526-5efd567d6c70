{"version": 3, "file": "configurations.js", "sourceRoot": "", "sources": ["../src/configurations.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,uCAAyB;AACzB,+CAAiC;AAEjC,uCAAyB;AACzB,wFAAmF;AACnF,wEAAoE;AAEpE,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,aAAa,CAAC;AACvD,MAAM,WAAW,GAAG,QAAQ,KAAK,aAAa,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,MAAM,CAAC;AAChF,MAAM,aAAa,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC;AAEvC,MAAM,cAAc,GAAG,IAAI,uDAAyB,CAAC,IAAI,wCAAkB,EAAE,CAAC,CAAC;AAG/E,MAAM,oBAAoB,GAAG,MAAM,CAAC,mBAAmB,CACrD,MAAM,CAAC,cAAc,CAAC,cAAc,CAAC,CACtC,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE;IAChB,MAAM,IAAI,GAAI,cAAsB,CAAC,IAAI,CAAC,CAAC;IAC3C,OAAO,OAAO,IAAI,KAAK,UAAU,IAAI,IAAI,KAAK,aAAa,CAAC;AAC9D,CAAC,CAAC,CAAC;AAEH,kBAAe,CAAC,GAAW,EAAE,EAAE;IAC7B,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC;IACvD,MAAM,IAAI,GAAG;QACX,GAAG,GAAG;QACN,QAAQ,EAAE,uBAAuB;QACjC,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI;QAC9B,QAAQ;QACR,SAAS,EAAE,GAAG,CAAC,SAAS,IAAI,OAAO;QACnC,OAAO,EAAE,GAAG,CAAC,OAAO,IAAI,MAAM;QAC9B,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC;QACtC,aAAa,EAAE,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC;QACxC,cAAc,EAAE,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC;QAC1C,aAAa,EAAE,MAAM,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,GAAG;QAC/C,0BAA0B,EACxB,MAAM,CAAC,GAAG,CAAC,0BAA0B,CAAC,IAAI,aAAa;QACzD,UAAU,EAAE,aAAa;QACzB,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC;QAC5C,uBAAuB,EAAE,MAAM,CAAC,GAAG,CAAC,uBAAuB,CAAC,IAAI,IAAI;QACpE,SAAS,EAAE,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;QAC/C,OAAO,EAAE,CAAC,GAAG,CAAC,OAAO,IAAI,SAAS,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC;QAC9C,sBAAsB,EAAE,MAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC,IAAI,EAAE;QAChE,sBAAsB,EAAE,MAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC,IAAI,EAAE;QAChE,2BAA2B,EAAE,MAAM,CAAC,GAAG,CAAC,2BAA2B,CAAC,IAAI,CAAC;QACzE,mBAAmB,EAAE,MAAM,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,GAAG;QAC3D,yBAAyB,EAAE,MAAM,CAAC,GAAG,CAAC,yBAAyB,CAAC,IAAI,CAAC;QACrE,WAAW,EAAE,CAAC,GAAG,CAAC,WAAW,IAAI,gBAAgB,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC;QAC7D,iCAAiC,EAC/B,MAAM,CAAC,GAAG,CAAC,iCAAiC,CAAC,IAAI,GAAG;QACtD,6BAA6B,EAAE,CAC7B,GAAG,CAAC,6BAA6B,IAAI,OAAO,CAC7C;aACE,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,MAAM,CAAC;QACd,kBAAkB,EAAE,GAAG,CAAC,kBAAkB,IAAI,IAAI;QAClD,wBAAwB,EAAE,CAAC,GAAG,CAAC,wBAAwB,IAAI,IAAI,CAAC;aAC7D,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,MAAM,CAAC;QACd,oBAAoB,EAAE,CAAC,GAAG,CAAC,oBAAoB,IAAI,KAAK,CAAC;aACtD,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,MAAM,CAAC;QACd,MAAM,EAAE,CAAC,GAAG,CAAC,MAAM,IAAI,iBAAiB,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC;QACpD,WAAW,EAAE,CAAC,GAAG,CAAC,WAAW,IAAI,YAAY,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC;QACzD,6BAA6B,EAAE,CAC7B,GAAG,CAAC,6BAA6B,IAAI,aAAa,CACnD;aACE,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,MAAM,CAAC;QACd,6BAA6B,EAAE,CAC7B,GAAG,CAAC,6BAA6B,IAAI,aAAa,CACnD;aACE,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,MAAM,CAAC;QACd,qCAAqC,EAAE,CACrC,GAAG,CAAC,qCAAqC,IAAI,OAAO,CACrD;aACE,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;QACxC,2BAA2B,EAAE,CAAC,GAAG,CAAC,2BAA2B,IAAI,OAAO,CAAC;aACtE,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;QACxC,2BAA2B,EAAE,CAAC,GAAG,CAAC,2BAA2B,IAAI,OAAO,CAAC;aACtE,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;QACxC,oBAAoB;QACpB,aAAa,EAAE,CAAC,GAAG,CAAC,aAAa,IAAI,aAAa,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC;QAC9D,QAAQ,EAAE;YACR,IAAI,EAAE,GAAG,CAAC,OAAO;YACjB,IAAI,EAAE,GAAG,CAAC,OAAO;YACjB,IAAI,EAAE,GAAG,CAAC,OAAO;YACjB,QAAQ,EAAE,GAAG,CAAC,OAAO;YACrB,QAAQ,EAAE,GAAG,CAAC,WAAW;YACzB,QAAQ,EAAE,GAAG,CAAC,OAAO;YACrB,QAAQ,EAAE,CAAC,SAAS,GAAG,uBAAuB,CAAC;YAC/C,WAAW,EAAE,IAAI;YACjB,GAAG,EACD,GAAG,CAAC,cAAc,KAAK,MAAM;gBAC3B,CAAC,CAAC;oBACE,kBAAkB,EAAE,KAAK;oBACzB,EAAE,EAAE,EAAE,CAAC,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC;iBAC1C;gBACH,CAAC,CAAC,KAAK;SACY;KAC1B,CAAC;IACF,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;AACnB,CAAC,CAAC"}