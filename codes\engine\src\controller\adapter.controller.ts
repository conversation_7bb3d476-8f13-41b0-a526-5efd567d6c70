import {
  Controller,
  Inject,
  HttpException,
  HttpStatus,
  Version,
  Post,
  Body,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import { AdapterService } from 'src/services/adapter.service';
import { GetHistoricalDto } from 'src/dto/get-historical.dto';
import { GetInstrumentDto } from 'src/dto/get-instrument.dto';
import { Historical } from 'src/interface/historical.interface';
import { Instrument } from 'src/interface/instrument.interface';
import { logDetail } from 'src/util/log-detail.util';

@ApiTags('Adapter API')
@Controller('adapter')
export class AdapterController {
  constructor(
    private readonly adapterService: AdapterService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  @Post('/historical')
  @ApiOperation({ summary: 'Get Historical Data from Bybit' })
  @ApiResponse({
    status: 200,
    description: 'List of Historical Data',
    isArray: true,
  })
  async fetchBybitHistorical(
    @Body() body: GetHistoricalDto,
  ): Promise<Historical[]> {
    try {
      body.start = new Date(body.start);
      body.end = new Date(body.end);

      const result = await this.adapterService.fetchBybitHistorical(body);

      return result ?? [];
    } catch (error: any) {
      const message = error?.message || 'Unknown error';

      this.logger.error(
        'Historical data fetch failed',
        logDetail({
          class: 'AppController',
          function: 'fetchBybitHistorical',
          body,
          error: error.stack || message,
        }),
      );

      throw new HttpException(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('/instrument')
  @ApiOperation({ summary: 'Get Instrument Data from Bybit' })
  @ApiResponse({
    status: 200,
    description: 'List of Instrument Data',
    isArray: true,
  })
  async fetchBybitInstrument(
    @Body() body: GetInstrumentDto,
  ): Promise<Instrument[]> {
    try {
      const result = await this.adapterService.fetchBybitInstrument(body);

      return result ?? [];
    } catch (error: any) {
      const message = error?.message || 'Unknown error';

      this.logger.error(
        'Instrument data fetch failed',
        logDetail({
          class: 'AppController',
          function: 'fetchBybitInstrument',
          body,
          error: error.stack || message,
        }),
      );

      throw new HttpException(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
