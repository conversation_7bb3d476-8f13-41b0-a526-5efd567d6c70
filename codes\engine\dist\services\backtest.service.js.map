{"version": 3, "file": "backtest.service.js", "sourceRoot": "", "sources": ["../../src/services/backtest.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,+CAAuD;AACvD,qCAAiC;AACjC,uEAA+C;AAC/C,6DAAqD;AAGrD,uFAA8E;AAC9E,2FAAkF;AAGlF,uEAA+D;AAC/D,yGAAgG;AAChG,+FAAqF;AAErF,uFAA+E;AAC/E,yEAAoE;AACpE,2GAAgG;AAGzF,IAAM,eAAe,GAArB,MAAM,eAAe;IAE0B;IACjC;IAFnB,YACoD,MAAc,EAC/C,sBAA8C;QADb,WAAM,GAAN,MAAM,CAAQ;QAC/C,2BAAsB,GAAtB,sBAAsB,CAAwB;IAC7D,CAAC;IAEL,KAAK,CAAC,oBAAoB,CAAC,KAAa;QACtC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC/D,MAAM,OAAO,GAAmB,EAAE,CAAC;YACnC,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;gBAC7B,MAAM,uBAAuB,GAC3B,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC;oBAC9C,MAAM;oBACN,QAAQ,EAAE,IAAA,wBAAc,EAAC,oBAAoB,CAAC;oBAC9C,KAAK,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;oBAC9B,GAAG,EAAE,IAAI,IAAI,EAAE;oBACf,KAAK,EAAE,IAAA,wBAAc,EAAC,4BAA4B,CAAC;oBACnD,IAAI,EAAE,KAAK;iBACZ,CAAC,CAAC;gBAEL,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC;oBAClE,MAAM,MAAM,GAAG,IAAA,qCAAc,EAAC,IAAI,EAAE,uBAAuB,CAAC,CAAC;oBAC7D,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACvB,CAAC;YACH,CAAC;YAED,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,2BAA2B,EAC3B,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,YAAY;gBACnB,QAAQ,EAAE,gBAAgB;gBAC1B,KAAK;gBACL,KAAK;aACN,CAAC,CACH,CAAC;YACF,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED,KAAK,CAAC,qBAAqB,CACzB,KAAa,EACb,mBAAkC;QAElC,IAAI,CAAC;YACH,IAAI,CAAC,KAAK,CAAC,MAAM;gBAAE,OAAO,EAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YAC/B,MAAM,OAAO,GAAmB,EAAE,CAAC;YAEnC,MAAM,uBAAuB,GAC3B,mBAAmB;gBACnB,CAAC,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC;oBAC/C,GAAG,KAAK;oBACR,MAAM;oBACN,QAAQ,EAAE,IAAA,wBAAc,EAAC,oBAAoB,CAAC;oBAC9C,KAAK,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;oBAC9B,GAAG,EAAE,IAAI,IAAI,EAAE;oBACf,KAAK,EAAE,IAAA,wBAAc,EAAC,4BAA4B,CAAC;oBACnD,IAAI,EAAE,KAAK;iBACZ,CAAC,CAAC,CAAC;YAEN,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC,EAAE,CAAC;gBAClE,MAAM,MAAM,GAAG,IAAA,qCAAc,EAAC,IAAI,EAAE,uBAAuB,CAAC,CAAC;gBAC7D,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvB,CAAC;YAED,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,2BAA2B,EAC3B,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,YAAY;gBACnB,QAAQ,EAAE,gBAAgB;gBAC1B,KAAK;gBACL,KAAK;aACN,CAAC,CACH,CAAC;YACF,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,KAAqB;QACxC,IAAI,CAAC;YACH,IAAI,CAAC,KAAK,CAAC,MAAM;gBACf,OAAO;oBACL,QAAQ,EAAE,EAAE;oBACZ,QAAQ,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC;oBACrB,OAAO,EAAE,IAAI,IAAI,EAAE;oBACnB,eAAe,EAAE,CAAC;oBAClB,iBAAiB,EAAE,CAAC;oBACpB,WAAW,EAAE,CAAC;oBACd,eAAe,EAAE,CAAC;oBAClB,cAAc,EAAE,CAAC;oBACjB,gBAAgB,EAAE,CAAC;oBACnB,kBAAkB,EAAE,CAAC;oBACrB,oBAAoB,EAAE,CAAC;oBACvB,oBAAoB,EAAE,CAAC;oBACvB,sBAAsB,EAAE,CAAC;oBACzB,gBAAgB,EAAE,CAAC;iBACpB,CAAC;YAEJ,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAC9B,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,QAAQ,CACnC,CAAC,MAAM,CAAC;YAET,IAAI,CAAC,WAAW;gBACd,OAAO;oBACL,QAAQ,EAAE,EAAE;oBACZ,QAAQ,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC;oBACrB,OAAO,EAAE,IAAI,IAAI,EAAE;oBACnB,eAAe,EAAE,CAAC;oBAClB,iBAAiB,EAAE,CAAC;oBACpB,WAAW,EAAE,CAAC;oBACd,eAAe,EAAE,CAAC;oBAClB,cAAc,EAAE,CAAC;oBACjB,gBAAgB,EAAE,CAAC;oBACnB,kBAAkB,EAAE,CAAC;oBACrB,oBAAoB,EAAE,CAAC;oBACvB,oBAAoB,EAAE,CAAC;oBACvB,sBAAsB,EAAE,CAAC;oBACzB,gBAAgB,EAAE,CAAC;iBACpB,CAAC;YACJ,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC;YAClE,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC;YACpC,MAAM,eAAe,GAAG,WAAW,GAAG,SAAS,CAAC;YAChD,MAAM,iBAAiB,GAAG,KAAK,CAAC,MAAM,CACpC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,SAAS,CACpC,CAAC,MAAM,CAAC;YACT,MAAM,WAAW,GAAG,MAAM,CACxB,CAAC,CAAC,WAAW,GAAG,eAAe,CAAC,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CACnD,CAAC;YACF,MAAM,eAAe,GAAG,IAAA,oDAAqB,EAAC,KAAK,CAAC,CAAC;YACrD,MAAM,oBAAoB,GAAG,IAAA,sEAA8B,EAAC,KAAK,CAAC,CAAC;YACnE,MAAM,kBAAkB,GAAG,IAAA,wDAAuB,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAClE,MAAM,oBAAoB,GAAG,IAAA,wDAAuB,EAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;YACtE,MAAM,sBAAsB,GAAG,IAAA,2DAAwB,EAAC,KAAK,CAAC,CAAC;YAC/D,MAAM,gBAAgB,GAAG,IAAA,qDAAsB,EAAC,KAAK,CAAC,CAAC;YACvD,MAAM,cAAc,GAAG,IAAA,sEAA6B,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YACpE,MAAM,gBAAgB,GAAG,IAAA,sEAA6B,EAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;YAExE,OAAO;gBACL,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ;gBAC3B,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI;gBACvB,OAAO,EAAE,IAAI,IAAI,EAAE;gBACnB,eAAe;gBACf,iBAAiB;gBACjB,WAAW;gBACX,sBAAsB;gBACtB,eAAe;gBACf,cAAc;gBACd,gBAAgB;gBAChB,kBAAkB;gBAClB,oBAAoB;gBACpB,oBAAoB;gBACpB,gBAAgB;aACjB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,gCAAgC,EAChC,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,YAAY;gBACnB,QAAQ,EAAE,qBAAqB;gBAC/B,KAAK;gBACL,KAAK;aACN,CAAC,CACH,CAAC;YACF,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;CACF,CAAA;AA3KY,0CAAe;0BAAf,eAAe;IAD3B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,eAAM,EAAC,sCAAuB,CAAC,CAAA;qCAA0B,gBAAM;QACvB,iDAAsB;GAHtD,eAAe,CA2K3B"}