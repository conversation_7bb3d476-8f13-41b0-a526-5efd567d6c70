"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OptimizeStaticPlanController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const nest_winston_1 = require("nest-winston");
const get_static_plan_dto_1 = require("../dto/get-static-plan.dto");
const optimize_static_plan_service_1 = require("../services/optimize-static-plan.service");
const log_detail_util_1 = require("../util/log-detail.util");
const winston_1 = require("winston");
let OptimizeStaticPlanController = class OptimizeStaticPlanController {
    appService;
    logger;
    constructor(appService, logger) {
        this.appService = appService;
        this.logger = logger;
    }
    async getOptimizedParam(body) {
        try {
            body.start = new Date(body.start);
            body.end = new Date(body.end);
            const result = await this.appService.optimize(body);
            return result ?? [];
        }
        catch (error) {
            const message = error?.message || 'Unknown error';
            this.logger.error('Optimize plan data fetch failed', (0, log_detail_util_1.logDetail)({
                class: 'AppController',
                function: 'getOptimizedParam',
                body,
                error: error.stack || message,
            }));
            throw new common_1.HttpException(message, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getOptimizedStaticPlan(body) {
        try {
            body.start = new Date(body.start);
            body.end = new Date(body.end);
            const result = await this.appService.getOptimizedStaticPlan(body);
            return result ?? [];
        }
        catch (error) {
            const message = error?.message || 'Unknown error';
            this.logger.error('Optimize plan data fetch failed', (0, log_detail_util_1.logDetail)({
                class: 'AppController',
                function: 'getOptimizedStaticPlan',
                body,
                error: error.stack || message,
            }));
            throw new common_1.HttpException(message, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.OptimizeStaticPlanController = OptimizeStaticPlanController;
__decorate([
    (0, common_1.Post)('optimized-param'),
    (0, swagger_1.ApiOperation)({ summary: 'Get Optimize Param' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of Optimize Plan Data',
        isArray: true,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_static_plan_dto_1.GetStaticPlanDto]),
    __metadata("design:returntype", Promise)
], OptimizeStaticPlanController.prototype, "getOptimizedParam", null);
__decorate([
    (0, common_1.Post)('optimized-plan'),
    (0, swagger_1.ApiOperation)({ summary: 'Get Optimized Static Plan' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of Optimized Static Plan Data',
        isArray: true,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_static_plan_dto_1.GetStaticPlanDto]),
    __metadata("design:returntype", Promise)
], OptimizeStaticPlanController.prototype, "getOptimizedStaticPlan", null);
exports.OptimizeStaticPlanController = OptimizeStaticPlanController = __decorate([
    (0, swagger_1.ApiTags)('Optimize Static Plan'),
    (0, common_1.Controller)('optimize-static-plan'),
    __param(1, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [optimize_static_plan_service_1.OptimizeStaticPlanService,
        winston_1.Logger])
], OptimizeStaticPlanController);
//# sourceMappingURL=optimize-static-plan.controller.js.map