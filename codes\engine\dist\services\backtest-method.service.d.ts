import { Logger } from 'winston';
import { GetBacktestMethodDto } from 'src/dto/get-backtest-method.dto';
import { Historical } from 'src/interface/historical.interface';
import { Pattern } from 'src/interface/pattern.interface';
import { Instrument } from 'src/interface/instrument.interface';
import { Plan } from 'src/interface/plan.interface';
import { BacktestService } from './backtest.service';
import { PatternService } from './pattern.service';
import { InstrumentService } from './instrument.service';
import { DynamicPlanService } from './dynamic-plan.service';
import { OptimizeStaticPlanService } from './optimize-static-plan.service';
import { BacktestPerformance } from 'src/interface/backtest-performance.interface';
import { MethodResult } from 'src/interface/method-result.interface';
import { StaticPlanService } from './static-plan.service';
import { HistoricalCacheService } from './historical-cache.service';
export declare class BacktestMethodService {
    private readonly logger;
    private readonly historicalCacheService;
    private readonly patternService;
    private readonly instrumentService;
    private readonly dynamicPlanService;
    private readonly backtestService;
    private readonly optimizeStaticPlanService;
    private readonly staticPlanService;
    constructor(logger: Logger, historicalCacheService: HistoricalCacheService, patternService: PatternService, instrumentService: InstrumentService, dynamicPlanService: DynamicPlanService, backtestService: BacktestService, optimizeStaticPlanService: OptimizeStaticPlanService, staticPlanService: StaticPlanService);
    getBacktestMethodResult(param: GetBacktestMethodDto, results?: MethodResult[], historical?: Historical[], historicalExecution?: Historical[], patterns?: Pattern[], instrument?: Instrument[], plan?: Plan[]): Promise<MethodResult[]>;
    getBacktestMethodPerformance(param: GetBacktestMethodDto, results?: MethodResult[], historical?: Historical[], historicalExecution?: Historical[], patterns?: Pattern[], instrument?: Instrument[], plan?: Plan[]): Promise<BacktestPerformance>;
    getBacktestMethodBoth(param: GetBacktestMethodDto, historical?: Historical[], historicalExecution?: Historical[], patterns?: Pattern[], instrument?: Instrument[], plan?: Plan[], results?: MethodResult[]): Promise<{
        result: MethodResult[];
        performance: BacktestPerformance;
    }>;
}
