"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InstrumentService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const nest_winston_1 = require("nest-winston");
const configurations_1 = __importDefault(require("../configurations"));
const instrument_entity_1 = require("../entity/instrument.entity");
const log_detail_util_1 = require("../util/log-detail.util");
const typeorm_2 = require("typeorm");
const winston_1 = require("winston");
let InstrumentService = class InstrumentService {
    InstrumentsRepository;
    logger;
    constructor(InstrumentsRepository, logger) {
        this.InstrumentsRepository = InstrumentsRepository;
        this.logger = logger;
    }
    async getInstrument(param) {
        try {
            const queryBuilder = this.InstrumentsRepository.createQueryBuilder('instrument')
                .leftJoinAndSelect('instrument.leverageFilter', 'leverageFilter')
                .leftJoinAndSelect('instrument.priceFilter', 'priceFilter')
                .leftJoinAndSelect('instrument.lotSizeFilter', 'lotSizeFilter');
            if (param.symbol) {
                queryBuilder.andWhere('instrument.symbol = :symbol', {
                    symbol: param.symbol,
                });
            }
            return await queryBuilder.getMany();
        }
        catch (err) {
            this.logger.error('Failed to read instrument data', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'read',
                error: err,
                param,
            }));
            throw new Error(`Failed to read instrument data`);
        }
    }
    async insert(param) {
        try {
            const symbols = param.map((p) => p.symbol);
            const existingRecords = await this.InstrumentsRepository.find({
                where: { symbol: (0, typeorm_2.In)(symbols) },
            });
            const existingMap = new Map(existingRecords.map((r) => [r.symbol, r]));
            const mergedRecords = param.map((record) => {
                const existing = existingMap.get(record.symbol);
                if (existing) {
                    return {
                        ...existing,
                        ...record,
                        auctionFeeInfo: {
                            ...(existing.auctionFeeInfo ?? {}),
                            ...(record.auctionFeeInfo ?? {}),
                        },
                        leverageFilter: {
                            ...(existing.leverageFilter ?? {}),
                            ...(record.leverageFilter ?? {}),
                        },
                        priceFilter: {
                            ...(existing.priceFilter ?? {}),
                            ...(record.priceFilter ?? {}),
                        },
                        lotSizeFilter: {
                            ...(existing.lotSizeFilter ?? {}),
                            ...(record.lotSizeFilter ?? {}),
                        },
                    };
                }
                else {
                    return record;
                }
            });
            return await this.InstrumentsRepository.save(mergedRecords);
        }
        catch (err) {
            this.logger.error('Failed to upsert instrument data', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'insert',
                error: err,
                param,
            }));
            throw new Error('Failed to upsert instrument data');
        }
    }
    async getSymbols() {
        try {
            const launchTimeThreshold = new Date().getTime() -
                (0, configurations_1.default)('SYMBOL_LAUNCH_TIME_THRESHOLD_DAYS') *
                    24 *
                    60 *
                    60 *
                    1000;
            const queryBuilder = this.InstrumentsRepository.createQueryBuilder('instrument');
            queryBuilder.andWhere('instrument.launchTime < :launchTimeThreshold', {
                launchTimeThreshold,
            });
            const symbols = await queryBuilder.select('instrument.symbol').getMany();
            return symbols.map((s) => s.symbol);
        }
        catch (err) {
            this.logger.error('Failed to read instrument data', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'getSymbols',
                error: err,
            }));
            throw new Error(`Failed to read instrument data`);
        }
    }
};
exports.InstrumentService = InstrumentService;
exports.InstrumentService = InstrumentService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(instrument_entity_1.InstrumentEntity)),
    __param(1, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        winston_1.Logger])
], InstrumentService);
//# sourceMappingURL=instrument.service.js.map