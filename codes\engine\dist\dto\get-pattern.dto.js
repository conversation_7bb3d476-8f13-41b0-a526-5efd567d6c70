"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetPatternDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const get_historical_dto_1 = require("./get-historical.dto");
const class_validator_1 = require("class-validator");
const configurations_1 = __importDefault(require("../configurations"));
class GetPatternDto extends get_historical_dto_1.GetHistoricalDto {
    trend;
    patternType;
    pattern;
}
exports.GetPatternDto = GetPatternDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Pattern Trend (e.g., up, down)',
        example: 'up',
        enum: ["up", "down", "neutral"],
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], GetPatternDto.prototype, "trend", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Pattern Types (e.g., candlestick, chart, indicator, etc.)',
        example: 'candlestick',
        enum: (0, configurations_1.default)('PATTERN_TYPES'),
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], GetPatternDto.prototype, "patternType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Candlestick Pattern (e.g., hammer, bullish_engulfing, etc.)',
        example: 'bullish_engulfing',
        enum: (0, configurations_1.default)('CANDLESTICK_PATTERNS'),
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], GetPatternDto.prototype, "pattern", void 0);
//# sourceMappingURL=get-pattern.dto.js.map