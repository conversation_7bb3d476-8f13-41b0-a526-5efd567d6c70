import { Inject, Injectable, Logger } from '@nestjs/common';
import axios, { AxiosError } from 'axios';
import configurations from '../configurations';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { logDetail } from 'src/util/log-detail.util';
import { DataSource } from 'typeorm';

@Injectable()
export class HealthCheckService {
  // Konfigurasi timeout dan daftar URL yang akan dicek
  private static readonly TIMEOUT_MS = 3000;
  private readonly healthCheckUrls = [configurations('HOST_INSTRUMENT_API')];

  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Mengecek apakah suatu URL dapat diakses (reachable).
   */
  private async checkDatabase(): Promise<'reachable' | 'unreachable'> {
    try {
      // Cek apakah koneksi database aktif
      if (!this.dataSource.isInitialized) {
        throw new Error('Database connection is not initialized');
      }

      // Coba menjalankan query sederhana
      await this.dataSource.query('SELECT 1');
      return 'reachable';
    } catch (err) {
      const error = err as Error;
      this.logger.error(
        `Database connection check failed: ${error.message}`,
        logDetail({
          class: 'HealthCheckService',
          function: 'checkDatabase',
          error,
        }),
      );
      return 'unreachable';
    }
  }
  /**
   * Mengecek apakah suatu URL dapat diakses (reachable).
   */
  private async checkUrl(url: string): Promise<'reachable' | 'unreachable'> {
    try {
      const response = await axios.head(url, {
        timeout: HealthCheckService.TIMEOUT_MS,
      });
      if (response.status === 200) {
        return 'reachable';
      }

      this.logger.warn(
        `Unexpected status ${response.status} from ${url}`,
        logDetail({
          class: 'HealthCheckService',
          function: 'checkUrl',
          url,
          status: response.status,
        }),
      );
    } catch (err) {
      const error = err as AxiosError;
      this.logger.error(
        `Health check failed for ${url}: ${error.message}`,
        logDetail({
          class: 'HealthCheckService',
          function: 'checkUrl',
          url,
          error: error.toJSON ? error.toJSON() : error,
        }),
      );
    }
    return 'unreachable';
  }

  /**
   * Mengecek seluruh URL dan mengembalikan hasil dalam bentuk objek.
   */
  async check(): Promise<{
    timestamp: string;
    results: Record<string, 'reachable' | 'unreachable'>;
  }> {
    const timestamp = new Date().toISOString();
    const results: Record<string, 'reachable' | 'unreachable'> = {};
    results['database'] = await this.checkDatabase();
    for (const url of this.healthCheckUrls) {
      results[url] = await this.checkUrl(url);
    }

    return { timestamp, results };
  }
}
