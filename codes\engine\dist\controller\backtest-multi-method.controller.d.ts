import { MethodResult } from 'src/interface/method-result.interface';
import { BacktestMultiMethodService } from 'src/services/backtest-multi-method.service';
import { Logger } from 'winston';
import { GetBacktestMultiMethodDto } from 'src/dto/get-backtest-multi-method.dto';
import { BacktestMultiPerformance } from 'src/interface/backtest-multi-performance.interface';
export declare class BacktestMultiMethodController {
    private readonly backtestMultiMethodService;
    private readonly logger;
    constructor(backtestMultiMethodService: BacktestMultiMethodService, logger: Logger);
    getResult(body: GetBacktestMultiMethodDto): Promise<MethodResult[]>;
    getPerformance(body: GetBacktestMultiMethodDto): Promise<BacktestMultiPerformance>;
    generateBoth(body: GetBacktestMultiMethodDto): Promise<{
        result: MethodResult[];
        performance: BacktestMultiPerformance;
    }>;
}
