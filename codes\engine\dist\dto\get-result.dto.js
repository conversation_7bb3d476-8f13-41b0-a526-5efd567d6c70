"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetResultDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class GetResultDto {
    id;
    date;
    interval;
    symbol;
    orderType;
    entryPercentByClose;
    riskPercent;
    rewardPercent;
    validityPeriod;
    entry;
    stopLoss;
    takeProfit;
    stopPercent;
    profitPercent;
    expiryDate;
    patternType;
    pattern;
}
exports.GetResultDto = GetResultDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique ID of the plan',
        example: 'BTCUSDT-5-2025-01-01T00:00:00.000Z',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], GetResultDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Date of the plan',
        example: '2025-01-01T00:00:00.000Z',
        type: String,
        format: 'date-time',
    }),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", Date)
], GetResultDto.prototype, "date", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Time interval',
        example: '5',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], GetResultDto.prototype, "interval", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trading symbol',
        example: 'BTCUSDT',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], GetResultDto.prototype, "symbol", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Order type (e.g., long or short)',
        example: 'long',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], GetResultDto.prototype, "orderType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Entry percent by close',
        example: -0.5,
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], GetResultDto.prototype, "entryPercentByClose", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Risk percent per trade',
        example: -2.0,
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], GetResultDto.prototype, "riskPercent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Reward percent per trade',
        example: 4.0,
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], GetResultDto.prototype, "rewardPercent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Period in samples for plan validity',
        example: 5,
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], GetResultDto.prototype, "validityPeriod", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Entry price',
        example: 93800.5,
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], GetResultDto.prototype, "entry", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Stop loss price',
        example: 92700.0,
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], GetResultDto.prototype, "stopLoss", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Take profit price',
        example: 96500.0,
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], GetResultDto.prototype, "takeProfit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Stop percent',
        example: -1.5,
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], GetResultDto.prototype, "stopPercent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Profit percent',
        example: 2.8,
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], GetResultDto.prototype, "profitPercent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Expiry date for the plan',
        example: '2025-01-10T00:00:00.000Z',
        type: String,
        format: 'date-time',
    }),
    (0, class_validator_1.IsDate)(),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", Date)
], GetResultDto.prototype, "expiryDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Pattern type (e.g., candlestick)',
        example: 'candlestick',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], GetResultDto.prototype, "patternType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Pattern name (e.g., hammer, engulfing)',
        example: 'hammer',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], GetResultDto.prototype, "pattern", void 0);
//# sourceMappingURL=get-result.dto.js.map