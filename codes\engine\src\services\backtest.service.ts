import { Inject, Injectable } from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import configurations from '../configurations';
import { logDetail } from 'src/util/log-detail.util';
import { MethodResult } from 'src/interface/method-result.interface';

import { calculateOpenPosition } from 'src/util/calculate-open-position.util';
import { calculateMaxConsecutive } from 'src/util/calculate-max-consecutive.util';
import { Plan } from 'src/interface/plan.interface';
import { BacktestPerformance } from 'src/interface/backtest-performance.interface';
import { generateResult } from 'src/util/generate-result.util';
import { calculateCummulativePercentage } from 'src/util/calculate-cummulative-percentage.util';
import { calculateRewardRiskRatio } from 'src/util/calculate-reward-risk-ratio.util';
import { Historical } from 'src/interface/historical.interface';
import { calculateHoldingPeriod } from 'src/util/calculate-holding-period.utl';
import { HistoricalCacheService } from './historical-cache.service';
import { calculateMaxStopProfitPercent } from 'src/util/calculate-max-stop-profit-percent.util';

@Injectable()
export class BacktestService {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    private readonly historicalCacheService: HistoricalCacheService,
  ) { }

  async getMultiSymbolResult(param: Plan[]): Promise<MethodResult[]> {
    try {
      const symbols = [...new Set(param.map((item) => item.symbol))];
      const results: MethodResult[] = [];
      for (const symbol of symbols) {
        const historicalExecutionData =
          await this.historicalCacheService.getHistorical({
            symbol,
            interval: configurations('EXECUTION_INTERVAL'),
            start: new Date(param[0].date),
            end: new Date(),
            limit: configurations('HISTORICAL_EXECUTION_LIMIT'),
            sort: 'ASC',
          });

        for (const item of param.filter((item) => item.symbol === symbol)) {
          const result = generateResult(item, historicalExecutionData);
          results.push(result);
        }
      }

      return results;
    } catch (error) {
      this.logger.error(
        'Failed to generate result',
        logDetail({
          class: 'AppService',
          function: 'generateResult',
          param,
          error,
        }),
      );
      throw new Error('Failed to generate result');
    }
  }

  async getSingleSymbolResult(
    param: Plan[],
    historicalExecution?: Historical[],
  ): Promise<MethodResult[]> {
    try {
      if (!param.length) return [];
      const symbol = param[0].symbol;
      const results: MethodResult[] = [];

      const historicalExecutionData =
        historicalExecution ??
        (await this.historicalCacheService.getHistorical({
          ...param,
          symbol,
          interval: configurations('EXECUTION_INTERVAL'),
          start: new Date(param[0].date),
          end: new Date(),
          limit: configurations('HISTORICAL_EXECUTION_LIMIT'),
          sort: 'ASC',
        }));

      for (const item of param.filter((item) => item.symbol === symbol)) {
        const result = generateResult(item, historicalExecutionData);
        results.push(result);
      }

      return results;
    } catch (error) {
      this.logger.error(
        'Failed to generate result',
        logDetail({
          class: 'AppService',
          function: 'generateResult',
          param,
          error,
        }),
      );
      throw new Error('Failed to generate result');
    }
  }

  async getPerformance(param: MethodResult[]): Promise<BacktestPerformance> {
    try {
      if (!param.length)
        return {
          methodId: '',
          fromDate: new Date(0),
          endDate: new Date(),
          totalValidTrade: 0,
          totalInvalidTrade: 0,
          probability: 0,
          maxOpenPosition: 0,
          maxStopPercent: 0,
          maxProfitPercent: 0,
          maxConsecutiveLoss: 0,
          maxConsecutiveProfit: 0,
          cumulativePercentage: 0,
          averageRewardRiskRatio: 0,
          maxHoldingPeriod: 0,
        };

      const totalProfit = param.filter(
        (item) => item.status === 'profit',
      ).length;

      if (!totalProfit)
        return {
          methodId: '',
          fromDate: new Date(0),
          endDate: new Date(),
          totalValidTrade: 0,
          totalInvalidTrade: 0,
          probability: 0,
          maxOpenPosition: 0,
          maxStopPercent: 0,
          maxProfitPercent: 0,
          maxConsecutiveLoss: 0,
          maxConsecutiveProfit: 0,
          cumulativePercentage: 0,
          averageRewardRiskRatio: 0,
          maxHoldingPeriod: 0,
        };
      const lossResult = param.filter((item) => item.status === 'loss');
      const totalLoss = lossResult.length;
      const totalValidTrade = totalProfit + totalLoss;
      const totalInvalidTrade = param.filter(
        (item) => item.status === 'invalid',
      ).length;
      const probability = Number(
        ((totalProfit / totalValidTrade) * 100).toFixed(2),
      );
      const maxOpenPosition = calculateOpenPosition(param);
      const cumulativePercentage = calculateCummulativePercentage(param);
      const maxConsecutiveLoss = calculateMaxConsecutive(param, 'loss');
      const maxConsecutiveProfit = calculateMaxConsecutive(param, 'profit');
      const averageRewardRiskRatio = calculateRewardRiskRatio(param);
      const maxHoldingPeriod = calculateHoldingPeriod(param);
      const maxStopPercent = calculateMaxStopProfitPercent(param, 'stop');
      const maxProfitPercent = calculateMaxStopProfitPercent(param, 'profit');

      return {
        methodId: param[0].methodId,
        fromDate: param[0].date,
        endDate: new Date(),
        totalValidTrade,
        totalInvalidTrade,
        probability,
        averageRewardRiskRatio,
        maxOpenPosition,
        maxStopPercent,
        maxProfitPercent,
        maxConsecutiveLoss,
        maxConsecutiveProfit,
        cumulativePercentage,
        maxHoldingPeriod,
      };
    } catch (error) {
      this.logger.error(
        'Failed to generate performance',
        logDetail({
          class: 'AppService',
          function: 'generatePerformance',
          param,
          error,
        }),
      );
      throw new Error('Failed to generate performance');
    }
  }
}
