"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HistoricalService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const nest_winston_1 = require("nest-winston");
const historical_entity_1 = require("../entity/historical.entity");
const log_detail_util_1 = require("../util/log-detail.util");
const typeorm_2 = require("typeorm");
const winston_1 = require("winston");
let HistoricalService = class HistoricalService {
    logger;
    HistoricalsRepository;
    constructor(logger, HistoricalsRepository) {
        this.logger = logger;
        this.HistoricalsRepository = HistoricalsRepository;
    }
    async countHistorical(symbol, interval, start, end) {
        try {
            const queryBuilder = this.HistoricalsRepository.createQueryBuilder('historical');
            if (symbol) {
                queryBuilder.andWhere('historical.symbol = :symbol', { symbol });
            }
            if (interval) {
                queryBuilder.andWhere('historical.interval = :interval', {
                    interval,
                });
            }
            if (start) {
                queryBuilder.andWhere('historical.date >= :start', { start });
            }
            if (end) {
                queryBuilder.andWhere('historical.date <= :end', { end });
            }
            return await queryBuilder.getCount();
        }
        catch (err) {
            this.logger.error('Failed to count historical data', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'count',
                error: err,
                param: { symbol, interval, start, end },
            }));
            throw new Error(`Failed to count historical data`);
        }
    }
    async getHistorical(param) {
        try {
            const { symbol, interval, start, end, limit, sort } = param;
            const queryBuilder = this.HistoricalsRepository.createQueryBuilder('historical');
            if (symbol) {
                queryBuilder.andWhere('historical.symbol = :symbol', { symbol });
            }
            if (interval) {
                queryBuilder.andWhere('historical.interval = :interval', {
                    interval,
                });
            }
            if (start) {
                queryBuilder.andWhere('historical.date >= :start', { start });
            }
            if (end) {
                queryBuilder.andWhere('historical.date <= :end', { end });
            }
            queryBuilder.orderBy('historical.date', sort).limit(limit);
            return await queryBuilder.getMany();
        }
        catch (err) {
            this.logger.error('Failed to read historical data', (0, log_detail_util_1.logDetail)({
                class: 'AppService',
                function: 'read',
                error: err,
                param,
            }));
            throw new Error(`Failed to read historical data`);
        }
    }
    async insertHistorical(param) {
        const recordsWithIds = param.map((record) => ({
            ...record,
            id: `${record.symbol}-${record.interval}-${record.date.toISOString()}`,
        }));
        await this.HistoricalsRepository.createQueryBuilder()
            .insert()
            .values(recordsWithIds)
            .orIgnore()
            .execute();
    }
    async deleteHistorical(param) {
        await this.HistoricalsRepository.createQueryBuilder()
            .delete()
            .where('symbol = :symbol AND interval = :interval', param)
            .execute();
    }
};
exports.HistoricalService = HistoricalService;
exports.HistoricalService = HistoricalService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __param(1, (0, typeorm_1.InjectRepository)(historical_entity_1.HistoricalEntity)),
    __metadata("design:paramtypes", [winston_1.Logger,
        typeorm_2.Repository])
], HistoricalService);
//# sourceMappingURL=historical.service.js.map