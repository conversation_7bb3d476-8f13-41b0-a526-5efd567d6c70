import { MethodStatusEntity } from 'src/entity/method-status.entity';
import { MethodStatus } from 'src/interface/method-status.interface';
import { Repository, UpdateResult } from 'typeorm';
import { Logger } from 'winston';
export declare class MethodStatusService {
    private readonly logger;
    private readonly MethodsStatusRepository;
    constructor(logger: Logger, MethodsStatusRepository: Repository<MethodStatusEntity>);
    getMethodStatusByMethodId(methodId: string): Promise<MethodStatusEntity | null>;
    getMethodStatus(): Promise<MethodStatusEntity[] | null>;
    getAllMethodStatus(): Promise<MethodStatusEntity[]>;
    insertMethodStatus(methodId: string, symbol: string, interval: string): Promise<void>;
    updateMethodStatus(param: MethodStatus): Promise<UpdateResult>;
    resetMethodStatus(interval: string): Promise<UpdateResult>;
    deleteMethodStatus(methodId: string): Promise<void>;
}
