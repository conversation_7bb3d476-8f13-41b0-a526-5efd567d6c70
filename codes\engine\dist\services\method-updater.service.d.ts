import { Logger } from 'winston';
import { MethodService } from './method.service';
import { MethodStatusService } from './method-status.service';
import { BacktestMethodService } from './backtest-method.service';
import { HistoricalCacheService } from './historical-cache.service';
import { InstrumentService } from './instrument.service';
export declare class MethodUpdaterService {
    private readonly logger;
    private readonly methodStatusService;
    private readonly methodService;
    private readonly backtestMethodService;
    private readonly historicalCacheService;
    private readonly instrumentService;
    constructor(logger: Logger, methodStatusService: MethodStatusService, methodService: MethodService, backtestMethodService: BacktestMethodService, historicalCacheService: HistoricalCacheService, instrumentService: InstrumentService);
    updateMethod(): Promise<void>;
    syncHelper(): Promise<void>;
}
