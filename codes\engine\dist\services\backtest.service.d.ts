import { Logger } from 'winston';
import { MethodResult } from 'src/interface/method-result.interface';
import { Plan } from 'src/interface/plan.interface';
import { BacktestPerformance } from 'src/interface/backtest-performance.interface';
import { Historical } from 'src/interface/historical.interface';
import { HistoricalCacheService } from './historical-cache.service';
export declare class BacktestService {
    private readonly logger;
    private readonly historicalCacheService;
    constructor(logger: Logger, historicalCacheService: HistoricalCacheService);
    getMultiSymbolResult(param: Plan[]): Promise<MethodResult[]>;
    getSingleSymbolResult(param: Plan[], historicalExecution?: Historical[]): Promise<MethodResult[]>;
    getPerformance(param: MethodResult[]): Promise<BacktestPerformance>;
}
