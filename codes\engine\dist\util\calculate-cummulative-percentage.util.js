"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.calculateCummulativePercentage = calculateCummulativePercentage;
function calculateCummulativePercentage(param) {
    const cumulativePercentage = param.reduce((acc, cur) => {
        if (cur.status === 'profit') {
            acc += Math.abs(cur.profitPercent);
        }
        if (cur.status === 'loss') {
            acc -= Math.abs(cur.stopPercent);
        }
        return acc;
    }, 0);
    return Number(cumulativePercentage.toFixed(2));
}
//# sourceMappingURL=calculate-cummulative-percentage.util.js.map