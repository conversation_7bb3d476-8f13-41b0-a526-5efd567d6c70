"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetHistoricalDto = void 0;
const class_validator_1 = require("class-validator");
const configurations_1 = __importDefault(require("../configurations"));
const swagger_1 = require("@nestjs/swagger");
class GetHistoricalDto {
    symbol;
    interval;
    start;
    end;
    limit;
    sort;
}
exports.GetHistoricalDto = GetHistoricalDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trading symbol (e.g., BTCUSDT)',
        example: 'BTCUSDT',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], GetHistoricalDto.prototype, "symbol", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Time interval (e.g., 1, 3, 5, 15)',
        example: '60',
        enum: (0, configurations_1.default)('INTERVALS'),
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], GetHistoricalDto.prototype, "interval", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Start date in ISO format',
        example: '2023-01-01T00:00:00Z',
    }),
    (0, class_validator_1.IsDateString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Date)
], GetHistoricalDto.prototype, "start", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'End date in ISO format',
        example: '2023-01-31T23:59:59Z',
    }),
    (0, class_validator_1.IsDateString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Date)
], GetHistoricalDto.prototype, "end", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Limit of data to return (e.g., 100)',
        example: 10000000000,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], GetHistoricalDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Sort of data to return (e.g., DESC)',
        example: 'ASC',
        enum: ['DESC', 'ASC'],
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], GetHistoricalDto.prototype, "sort", void 0);
//# sourceMappingURL=get-historical.dto.js.map