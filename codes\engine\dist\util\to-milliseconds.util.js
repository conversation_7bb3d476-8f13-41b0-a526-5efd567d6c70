"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toMiliseconds = toMiliseconds;
function toMiliseconds(interval) {
    const minute = 1000 * 60;
    switch (interval.toUpperCase()) {
        case 'M':
            return minute * 60 * 24 * 30;
        case 'W':
            return minute * 60 * 24 * 7;
        case 'D':
            return minute * 60 * 24;
        default:
            const minutes = parseInt(interval);
            return isNaN(minutes) ? minute : minute * minutes;
    }
}
//# sourceMappingURL=to-milliseconds.util.js.map