{"version": 3, "file": "backtest-multi-method.controller.js", "sourceRoot": "", "sources": ["../../src/controller/backtest-multi-method.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAOwB;AACxB,6CAAqE;AACrE,+CAAuD;AAEvD,6FAAwF;AACxF,6DAAqD;AACrD,qCAAiC;AACjC,wFAAkF;AAK3E,IAAM,6BAA6B,GAAnC,MAAM,6BAA6B;IAErB;IACiC;IAFpD,YACmB,0BAAsD,EACrB,MAAc;QAD/C,+BAA0B,GAA1B,0BAA0B,CAA4B;QACrB,WAAM,GAAN,MAAM,CAAQ;IAC/D,CAAC;IAIE,AAAN,KAAK,CAAC,SAAS,CACL,IAA+B;QAEvC,IAAI,CAAC;YACH,MAAM,MAAM,GACV,MAAM,IAAI,CAAC,0BAA0B,CAAC,4BAA4B,CAChE,IAAI,CACL,CAAC;YACJ,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,OAAO,GAAG,KAAK,EAAE,OAAO,IAAI,2BAA2B,CAAC;YAE9D,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,iDAAiD,EACjD,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,+BAA+B;gBACtC,QAAQ,EAAE,WAAW;gBACrB,IAAI;gBACJ,KAAK,EAAE,KAAK,CAAC,KAAK,IAAI,OAAO;aAC9B,CAAC,CACH,CAAC;YAEF,MAAM,IAAI,sBAAa,CAAC,OAAO,EAAE,mBAAU,CAAC,qBAAqB,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAIK,AAAN,KAAK,CAAC,cAAc,CACV,IAA+B;QAEvC,IAAI,CAAC;YACH,MAAM,WAAW,GACf,MAAM,IAAI,CAAC,0BAA0B,CAAC,iCAAiC,CACrE,IAAI,CACL,CAAC;YACJ,OAAO,WAAW,CAAC;QACrB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,OAAO,GAAG,KAAK,EAAE,OAAO,IAAI,gCAAgC,CAAC;YAEnE,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,sDAAsD,EACtD,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,+BAA+B;gBACtC,QAAQ,EAAE,gBAAgB;gBAC1B,IAAI;gBACJ,KAAK,EAAE,KAAK,CAAC,KAAK,IAAI,OAAO;aAC9B,CAAC,CACH,CAAC;YAEF,MAAM,IAAI,sBAAa,CAAC,OAAO,EAAE,mBAAU,CAAC,qBAAqB,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAwCK,AAAN,KAAK,CAAC,YAAY,CAAS,IAA+B;QAIxD,IAAI,CAAC;YACH,MAAM,MAAM,GACV,MAAM,IAAI,CAAC,0BAA0B,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAC;YACzE,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,OAAO,GACX,KAAK,EAAE,OAAO,IAAI,gDAAgD,CAAC;YAErE,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,yBAAyB,EACzB,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,+BAA+B;gBACtC,QAAQ,EAAE,cAAc;gBACxB,IAAI;gBACJ,KAAK,EAAE,KAAK,CAAC,KAAK,IAAI,OAAO;aAC9B,CAAC,CACH,CAAC;YAEF,MAAM,IAAI,sBAAa,CAAC,OAAO,EAAE,mBAAU,CAAC,qBAAqB,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;CACF,CAAA;AA7HY,sEAA6B;AAQlC;IAFL,IAAA,aAAI,EAAC,QAAQ,CAAC;IACd,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC;IAEnD,WAAA,IAAA,aAAI,GAAE,CAAA;;qCAAO,yDAAyB;;8DAuBxC;AAIK;IAFL,IAAA,aAAI,EAAC,aAAa,CAAC;IACnB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,+BAA+B,EAAE,CAAC;IAExD,WAAA,IAAA,aAAI,GAAE,CAAA;;qCAAO,yDAAyB;;mEAuBxC;AAwCK;IAtCL,IAAA,aAAI,EAAC,oBAAoB,CAAC;IAC1B,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,yCAAyC,EAAE,CAAC;IACpE,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,iDAAiD;QAC9D,MAAM,EAAE;YACN,OAAO,EAAE;gBACP,MAAM,EAAE;oBACN;wBACE,QAAQ,EAAE,QAAQ;wBAClB,IAAI,EAAE,0BAA0B;wBAChC,MAAM,EAAE,QAAQ;wBAChB,QAAQ,EAAE,0BAA0B;wBACpC,UAAU,EAAE,0BAA0B;wBACtC,QAAQ,EAAE,GAAG;wBACb,MAAM,EAAE,SAAS;wBACjB,SAAS,EAAE,MAAM;wBACjB,KAAK,EAAE,KAAK;wBACZ,QAAQ,EAAE,IAAI;wBACd,UAAU,EAAE,KAAK;wBACjB,WAAW,EAAE,EAAE;wBACf,aAAa,EAAE,EAAE;wBACjB,UAAU,EAAE,0BAA0B;qBACvC;iBACF;gBACD,WAAW,EAAE;oBACX,QAAQ,EAAE,QAAQ;oBAClB,QAAQ,EAAE,0BAA0B;oBACpC,OAAO,EAAE,0BAA0B;oBACnC,eAAe,EAAE,EAAE;oBACnB,iBAAiB,EAAE,CAAC;oBACpB,WAAW,EAAE,IAAI;oBACjB,eAAe,EAAE,CAAC;oBAClB,kBAAkB,EAAE,CAAC;iBACtB;aACF;SACF;KACF,CAAC;IACkB,WAAA,IAAA,aAAI,GAAE,CAAA;;qCAAO,yDAAyB;;iEAwBzD;wCA5HU,6BAA6B;IAFzC,IAAA,iBAAO,EAAC,uBAAuB,CAAC;IAChC,IAAA,mBAAU,EAAC,uBAAuB,CAAC;IAI/B,WAAA,IAAA,eAAM,EAAC,sCAAuB,CAAC,CAAA;qCADa,0DAA0B;QACb,gBAAM;GAHvD,6BAA6B,CA6HzC"}