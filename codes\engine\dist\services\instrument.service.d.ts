import { GetInstrumentDto } from 'src/dto/get-instrument.dto';
import { InstrumentEntity } from 'src/entity/instrument.entity';
import { Instrument } from 'src/interface/instrument.interface';
import { Repository } from 'typeorm';
import { Logger } from 'winston';
export declare class InstrumentService {
    private readonly InstrumentsRepository;
    private readonly logger;
    constructor(InstrumentsRepository: Repository<InstrumentEntity>, logger: Logger);
    getInstrument(param: GetInstrumentDto): Promise<InstrumentEntity[]>;
    insert(param: Instrument[]): Promise<InstrumentEntity[]>;
    getSymbols(): Promise<string[]>;
}
