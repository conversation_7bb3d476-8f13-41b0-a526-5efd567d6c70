"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const winston = __importStar(require("winston"));
const nest_winston_2 = require("nest-winston");
const path = __importStar(require("path"));
const configurations_1 = __importDefault(require("./configurations"));
const pattern_controller_1 = require("./controller/pattern.controller");
const candlestick_service_1 = require("./services/candlestick.service");
const candlestick_pattern_service_1 = require("./services/candlestick-pattern.service");
const pattern_service_1 = require("./services/pattern.service");
const health_check_controller_1 = require("./controller/health-check.controller");
const health_check_service_1 = require("./services/health-check.service");
const backtest_controller_1 = require("./controller/backtest.controller");
const static_plan_controller_1 = require("./controller/static-plan.controller");
const backtest_service_1 = require("./services/backtest.service");
const static_plan_service_1 = require("./services/static-plan.service");
const historical_service_1 = require("./services/historical.service");
const optimize_static_plan_controller_1 = require("./controller/optimize-static-plan.controller");
const optimize_static_plan_service_1 = require("./services/optimize-static-plan.service");
const dynamic_plan_controller_1 = require("./controller/dynamic-plan.controller");
const dynamic_plan_service_1 = require("./services/dynamic-plan.service");
const instrument_service_1 = require("./services/instrument.service");
const backtest_method_controller_1 = require("./controller/backtest-method.controller");
const backtest_method_service_1 = require("./services/backtest-method.service");
const typeorm_1 = require("@nestjs/typeorm");
const historical_entity_1 = require("./entity/historical.entity");
const instrument_entity_1 = require("./entity/instrument.entity");
const historical_controller_1 = require("./controller/historical.controller");
const instrument_controller_1 = require("./controller/instrument.controller");
const adapter_controller_1 = require("./controller/adapter.controller");
const adapter_service_1 = require("./services/adapter.service");
const method_param_entity_1 = require("./entity/method-param.entity");
const method_result_entity_1 = require("./entity/method-result.entity");
const method_performance_entity_1 = require("./entity/method-performance.entity");
const schedule_1 = require("@nestjs/schedule");
const task_service_1 = require("./services/task.service");
const historical_ingestion_service_1 = require("./services/historical-ingestion.service");
const method_service_1 = require("./services/method.service");
const method_controller_1 = require("./controller/method.controller");
const method_ingestion_service_1 = require("./services/method-ingestion.service");
const method_status_service_1 = require("./services/method-status.service");
const method_updater_service_1 = require("./services/method-updater.service");
const method_status_entity_1 = require("./entity/method-status.entity");
const instrument_ingestion_service_1 = require("./services/instrument-ingestion.service");
const historical_cache_service_1 = require("./services/historical-cache.service");
const backtest_multi_method_controller_1 = require("./controller/backtest-multi-method.controller");
const backtest_multi_method_service_1 = require("./services/backtest-multi-method.service");
const isProd = (0, configurations_1.default)('NODE_ENV') === 'production';
const engineMode = (0, configurations_1.default)('ENGINE_MODE');
let moduleConfig = {};
switch (engineMode) {
    case 'service':
        moduleConfig = {
            controllers: [
                adapter_controller_1.AdapterController,
                backtest_method_controller_1.BacktestMethodController,
                backtest_multi_method_controller_1.BacktestMultiMethodController,
                backtest_controller_1.BacktestController,
                dynamic_plan_controller_1.DynamicPlanController,
                health_check_controller_1.HealthCheckController,
                instrument_controller_1.InstrumentController,
                historical_controller_1.HistoricalController,
                optimize_static_plan_controller_1.OptimizeStaticPlanController,
                pattern_controller_1.PatternController,
                static_plan_controller_1.StaticPlanController,
                method_controller_1.MethodController,
            ],
            providers: [
                method_service_1.MethodService,
                adapter_service_1.AdapterService,
                backtest_method_service_1.BacktestMethodService,
                backtest_multi_method_service_1.BacktestMultiMethodService,
                backtest_service_1.BacktestService,
                candlestick_pattern_service_1.CandlestickPatternService,
                candlestick_service_1.CandlestickService,
                dynamic_plan_service_1.DynamicPlanService,
                health_check_service_1.HealthCheckService,
                historical_service_1.HistoricalService,
                instrument_service_1.InstrumentService,
                optimize_static_plan_service_1.OptimizeStaticPlanService,
                pattern_service_1.PatternService,
                static_plan_service_1.StaticPlanService,
                instrument_ingestion_service_1.InstrumentIngestionService,
                historical_ingestion_service_1.HistoricalIngestionService,
                historical_cache_service_1.HistoricalCacheService,
                method_status_service_1.MethodStatusService,
                method_updater_service_1.MethodUpdaterService,
                method_ingestion_service_1.MethodIngestionService,
                task_service_1.TaskService,
            ],
        };
        break;
    case 'worker':
        moduleConfig = {
            controllers: [],
            providers: [
                method_service_1.MethodService,
                adapter_service_1.AdapterService,
                backtest_method_service_1.BacktestMethodService,
                backtest_service_1.BacktestService,
                candlestick_pattern_service_1.CandlestickPatternService,
                candlestick_service_1.CandlestickService,
                dynamic_plan_service_1.DynamicPlanService,
                health_check_service_1.HealthCheckService,
                historical_service_1.HistoricalService,
                instrument_service_1.InstrumentService,
                optimize_static_plan_service_1.OptimizeStaticPlanService,
                pattern_service_1.PatternService,
                static_plan_service_1.StaticPlanService,
                instrument_ingestion_service_1.InstrumentIngestionService,
                historical_ingestion_service_1.HistoricalIngestionService,
                historical_cache_service_1.HistoricalCacheService,
                method_status_service_1.MethodStatusService,
                method_updater_service_1.MethodUpdaterService,
                method_ingestion_service_1.MethodIngestionService,
                task_service_1.TaskService,
            ],
        };
        break;
    default:
        throw new Error(`Invalid engine mode: ${engineMode}`);
}
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forRoot((0, configurations_1.default)('DATABASE')),
            typeorm_1.TypeOrmModule.forFeature([
                historical_entity_1.HistoricalEntity,
                instrument_entity_1.InstrumentEntity,
                method_param_entity_1.MethodParamEntity,
                method_result_entity_1.MethodResultEntity,
                method_performance_entity_1.MethodPerformanceEntity,
                method_status_entity_1.MethodStatusEntity,
            ]),
            schedule_1.ScheduleModule.forRoot(),
            nest_winston_1.WinstonModule.forRoot({
                transports: [
                    ...(isProd
                        ? [
                            new winston.transports.File({
                                filename: path.join((0, configurations_1.default)('LOG_DIR'), 'app.log'),
                                level: (0, configurations_1.default)('LOG_LEVEL') || 'warn',
                                maxsize: 10 * 1024 * 1024,
                                maxFiles: 5,
                                format: winston.format.combine(winston.format.timestamp(), winston.format.json()),
                            }),
                        ]
                        : [
                            new winston.transports.Console({
                                level: (0, configurations_1.default)('LOG_LEVEL') || 'debug',
                                format: winston.format.combine(winston.format.timestamp(), winston.format.ms(), nest_winston_2.utilities.format.nestLike((0, configurations_1.default)('APP_NAME'), {
                                    colors: true,
                                    prettyPrint: true,
                                })),
                            }),
                        ]),
                ],
            }),
        ],
        ...moduleConfig,
    })
], AppModule);
//# sourceMappingURL=app.module.js.map