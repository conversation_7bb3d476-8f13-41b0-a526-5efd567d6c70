import {
  Controller,
  Get,
  HttpException,
  HttpStatus,
  Inject,
} from '@nestjs/common';
;
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import { logDetail } from 'src/util/log-detail.util';
import { HealthCheckService } from 'src/services/health-check.service';

@Controller('health')
export class HealthCheckController {
  constructor(
    private readonly healthCheckService: HealthCheckService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  @Get('/ready')
  async check(): Promise<{
    status: string;
    timestamp: string;
    results: Record<string, 'reachable' | 'unreachable'>;
    message?: string;
  }> {
    const { timestamp, results } = await this.healthCheckService.check();

    const hasUnreachable = Object.values(results).includes('unreachable');

    if (hasUnreachable) {
      this.logger.warn(
        'Some services are unreachable',
        logDetail({
          class: 'HealthCheckController',
          function: 'check',
          results,
        }),
      );

      throw new HttpException(
        {
          status: 'error',
          message: 'Some services are unreachable',
          timestamp,
          results,
        },
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }

    return {
      status: 'ok',
      timestamp,
      results,
    };
  }
}
