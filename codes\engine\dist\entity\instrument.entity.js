"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InstrumentEntity = exports.AuctionFeeInfoEntity = exports.LotSizeFilterEntity = exports.PriceFilterEntity = exports.LeverageFilterEntity = void 0;
const typeorm_1 = require("typeorm");
let LeverageFilterEntity = class LeverageFilterEntity {
    id;
    minLeverage;
    maxLeverage;
    leverageStep;
};
exports.LeverageFilterEntity = LeverageFilterEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], LeverageFilterEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'float' }),
    __metadata("design:type", Number)
], LeverageFilterEntity.prototype, "minLeverage", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'float' }),
    __metadata("design:type", Number)
], LeverageFilterEntity.prototype, "maxLeverage", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'float' }),
    __metadata("design:type", Number)
], LeverageFilterEntity.prototype, "leverageStep", void 0);
exports.LeverageFilterEntity = LeverageFilterEntity = __decorate([
    (0, typeorm_1.Entity)('leverage-filter')
], LeverageFilterEntity);
let PriceFilterEntity = class PriceFilterEntity {
    id;
    minPrice;
    maxPrice;
    tickSize;
};
exports.PriceFilterEntity = PriceFilterEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], PriceFilterEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'float' }),
    __metadata("design:type", Number)
], PriceFilterEntity.prototype, "minPrice", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'float' }),
    __metadata("design:type", Number)
], PriceFilterEntity.prototype, "maxPrice", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'float' }),
    __metadata("design:type", Number)
], PriceFilterEntity.prototype, "tickSize", void 0);
exports.PriceFilterEntity = PriceFilterEntity = __decorate([
    (0, typeorm_1.Entity)('price-filter')
], PriceFilterEntity);
let LotSizeFilterEntity = class LotSizeFilterEntity {
    id;
    maxOrderQty;
    minOrderQty;
    qtyStep;
    postOnlyMaxOrderQty;
    maxMktOrderQty;
    minNotionalValue;
};
exports.LotSizeFilterEntity = LotSizeFilterEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], LotSizeFilterEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'float' }),
    __metadata("design:type", Number)
], LotSizeFilterEntity.prototype, "maxOrderQty", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'float' }),
    __metadata("design:type", Number)
], LotSizeFilterEntity.prototype, "minOrderQty", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'float' }),
    __metadata("design:type", Number)
], LotSizeFilterEntity.prototype, "qtyStep", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'float' }),
    __metadata("design:type", Number)
], LotSizeFilterEntity.prototype, "postOnlyMaxOrderQty", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'float' }),
    __metadata("design:type", Number)
], LotSizeFilterEntity.prototype, "maxMktOrderQty", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'float' }),
    __metadata("design:type", Number)
], LotSizeFilterEntity.prototype, "minNotionalValue", void 0);
exports.LotSizeFilterEntity = LotSizeFilterEntity = __decorate([
    (0, typeorm_1.Entity)('lot-size-filter')
], LotSizeFilterEntity);
let AuctionFeeInfoEntity = class AuctionFeeInfoEntity {
    id;
    auctionFeeRate;
    takerFeeRate;
    makerFeeRate;
};
exports.AuctionFeeInfoEntity = AuctionFeeInfoEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], AuctionFeeInfoEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], AuctionFeeInfoEntity.prototype, "auctionFeeRate", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], AuctionFeeInfoEntity.prototype, "takerFeeRate", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], AuctionFeeInfoEntity.prototype, "makerFeeRate", void 0);
exports.AuctionFeeInfoEntity = AuctionFeeInfoEntity = __decorate([
    (0, typeorm_1.Entity)('auction-fee-info')
], AuctionFeeInfoEntity);
let InstrumentEntity = class InstrumentEntity {
    id;
    symbol;
    launchTime;
    listedTime;
    priceScale;
    leverageFilter;
    priceFilter;
    lotSizeFilter;
    fundingInterval;
    copyTrading;
    upperFundingRate;
    lowerFundingRate;
    auctionFeeInfo;
};
exports.InstrumentEntity = InstrumentEntity;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], InstrumentEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: false }),
    __metadata("design:type", String)
], InstrumentEntity.prototype, "symbol", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'bigint', nullable: true }),
    __metadata("design:type", Number)
], InstrumentEntity.prototype, "launchTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'bigint', nullable: true }),
    __metadata("design:type", Number)
], InstrumentEntity.prototype, "listedTime", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Number)
], InstrumentEntity.prototype, "priceScale", void 0);
__decorate([
    (0, typeorm_1.OneToOne)(() => LeverageFilterEntity, { cascade: true, eager: true }),
    (0, typeorm_1.JoinColumn)(),
    __metadata("design:type", LeverageFilterEntity)
], InstrumentEntity.prototype, "leverageFilter", void 0);
__decorate([
    (0, typeorm_1.OneToOne)(() => PriceFilterEntity, { cascade: true, eager: true }),
    (0, typeorm_1.JoinColumn)(),
    __metadata("design:type", PriceFilterEntity)
], InstrumentEntity.prototype, "priceFilter", void 0);
__decorate([
    (0, typeorm_1.OneToOne)(() => LotSizeFilterEntity, { cascade: true, eager: true }),
    (0, typeorm_1.JoinColumn)(),
    __metadata("design:type", LotSizeFilterEntity)
], InstrumentEntity.prototype, "lotSizeFilter", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Number)
], InstrumentEntity.prototype, "fundingInterval", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], InstrumentEntity.prototype, "copyTrading", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'float', nullable: true }),
    __metadata("design:type", Number)
], InstrumentEntity.prototype, "upperFundingRate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'float', nullable: true }),
    __metadata("design:type", Number)
], InstrumentEntity.prototype, "lowerFundingRate", void 0);
__decorate([
    (0, typeorm_1.OneToOne)(() => AuctionFeeInfoEntity, { cascade: true, eager: true }),
    (0, typeorm_1.JoinColumn)(),
    __metadata("design:type", AuctionFeeInfoEntity)
], InstrumentEntity.prototype, "auctionFeeInfo", void 0);
exports.InstrumentEntity = InstrumentEntity = __decorate([
    (0, typeorm_1.Entity)('instrument'),
    (0, typeorm_1.Index)(['symbol'])
], InstrumentEntity);
//# sourceMappingURL=instrument.entity.js.map