import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { GetHistoricalDto } from 'src/dto/get-historical.dto';
import { HistoricalEntity } from 'src/entity/historical.entity';
import { Historical } from 'src/interface/historical.interface';
import { logDetail } from 'src/util/log-detail.util';
import { Repository } from 'typeorm';
import { Logger } from 'winston';

@Injectable()
export class HistoricalService {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    @InjectRepository(HistoricalEntity)
    private readonly HistoricalsRepository: Repository<HistoricalEntity>,
  ) {}

  async countHistorical(
    symbol: string,
    interval: string,
    start?: Date,
    end?: Date,
  ): Promise<number> {
    try {
      const queryBuilder =
        this.HistoricalsRepository.createQueryBuilder('historical');

      // Add conditions based on provided parameters
      if (symbol) {
        queryBuilder.andWhere('historical.symbol = :symbol', { symbol });
      }

      if (interval) {
        queryBuilder.andWhere('historical.interval = :interval', {
          interval,
        });
      }

      if (start) {
        queryBuilder.andWhere('historical.date >= :start', { start });
      }

      if (end) {
        queryBuilder.andWhere('historical.date <= :end', { end });
      }

      return await queryBuilder.getCount();
    } catch (err) {
      this.logger.error(
        'Failed to count historical data',
        logDetail({
          class: 'AppService',
          function: 'count',
          error: err,
          param: { symbol, interval, start, end },
        }),
      );
      throw new Error(`Failed to count historical data`);
    }
  }

  async getHistorical(param: GetHistoricalDto): Promise<Historical[]> {
    try {
      const { symbol, interval, start, end, limit, sort } = param;

      const queryBuilder =
        this.HistoricalsRepository.createQueryBuilder('historical');

      // Add conditions based on provided parameters
      if (symbol) {
        queryBuilder.andWhere('historical.symbol = :symbol', { symbol });
      }

      if (interval) {
        queryBuilder.andWhere('historical.interval = :interval', {
          interval,
        });
      }

      if (start) {
        queryBuilder.andWhere('historical.date >= :start', { start });
      }

      if (end) {
        queryBuilder.andWhere('historical.date <= :end', { end });
      }

      // Order by date descending and limit results
      queryBuilder.orderBy('historical.date', sort).limit(limit);

      return await queryBuilder.getMany();
    } catch (err) {
      this.logger.error(
        'Failed to read historical data',
        logDetail({
          class: 'AppService',
          function: 'read',
          error: err,
          param,
        }),
      );
      throw new Error(`Failed to read historical data`);
    }
  }

  async insertHistorical(param: Historical[]): Promise<void> {
    const recordsWithIds = param.map((record) => ({
      ...record,
      id: `${record.symbol}-${record.interval}-${record.date.toISOString()}`,
    }));

    await this.HistoricalsRepository.createQueryBuilder()
      .insert()
      .values(recordsWithIds)
      .orIgnore()
      .execute();
  }

  async deleteHistorical(param: {
    symbol: string;
    interval: string;
  }): Promise<void> {
    await this.HistoricalsRepository.createQueryBuilder()
      .delete()
      .where('symbol = :symbol AND interval = :interval', param)
      .execute();
  }
}
