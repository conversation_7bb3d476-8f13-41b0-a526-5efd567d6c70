import { CandlestickService } from './candlestick.service';
import { Historical } from '../interface/historical.interface';
export declare class CandlestickPatternService {
    private readonly candlestickService;
    constructor(candlestickService: CandlestickService);
    marubozu_white(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    marubozu_black(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    bullish_spinning_top(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    bearishSpinningTop(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    grave_stone_doji(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    dragon_fly_doji(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    long_legged_doji(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    bullish_belt_hold(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    hammer(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    inverted_hammer(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    bullish_engulfing(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    piercing(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    bullish_harami(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    bullish_kicker(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    bullish_meeting_line(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    matching_low(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    tweezer_bottom(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    bullish_separating_lines(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    morning_star(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    bullish_abandoned_baby(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    three_white_soldier(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    downside_gap_two_soldiers(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    two_soldiers(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    three_inside_up(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    three_outside_up(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    three_stars_in_the_south(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    bullish_stick_sandwich(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    upside_tasuki_gap(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    bullish_side_by_side_white_lines(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    bullish_tri_star(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    bullish_breakway(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    rising_three(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    bearish_belt_hold(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    hanging_man(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    shooting_star(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    bearish_engulfing(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    dark_cloud_cover(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    bearish_harami(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    bearish_kicker(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    bearish_meeting_line(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    matching_high(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    tweezer_top(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    bearish_separating_lines(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    evening_star(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    bearish_abandoned_baby(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    three_black_crows(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    upside_gap_two_crows(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    two_crows(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    three_inside_down(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    three_outside_down(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    advance_block(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    bearish_stick_sandwich(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    downside_tasuki_gap(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    bearish_side_by_side_white_lines(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    bearish_tri_star(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    bearish_breakway(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
    failing_three(params: {
        candles: Historical[];
        trend: string;
    }): boolean;
}
