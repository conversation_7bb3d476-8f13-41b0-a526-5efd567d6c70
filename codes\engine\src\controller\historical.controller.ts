import {
  Controller,
  Inject,
  HttpException,
  HttpStatus,
  Post,
  Body,
} from '@nestjs/common';

import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import { GetHistoricalDto } from 'src/dto/get-historical.dto';
import { logDetail } from 'src/util/log-detail.util';
import { Historical } from 'src/interface/historical.interface';
import { HistoricalCacheService } from 'src/services/historical-cache.service';

@ApiTags('Historical Data')
@Controller('historical')
export class HistoricalController {
  constructor(
    private readonly historicalCacheService: HistoricalCacheService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Get Historical Data from Database' })
  @ApiResponse({
    status: 200,
    description: 'List of Historical Data',
    isArray: true,
  })
  async get(@Body() body: GetHistoricalDto): Promise<Historical[]> {
    try {
      // Validasi tanggal jika diperlukan
      body.start = new Date(body.start);
      body.end = new Date(body.end);

      const result = await this.historicalCacheService.getHistorical(body);

      return result ?? [];
    } catch (error: any) {
      const message = error?.message || 'Unknown error';

      this.logger.error(
        'Historical data fetch failed',
        logDetail({
          class: 'HistoricalController',
          function: 'get',
          body,
          error: error.stack || message,
        }),
      );

      throw new HttpException(message, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
