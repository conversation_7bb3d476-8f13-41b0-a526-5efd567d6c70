"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BacktestMultiMethodController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const nest_winston_1 = require("nest-winston");
const backtest_multi_method_service_1 = require("../services/backtest-multi-method.service");
const log_detail_util_1 = require("../util/log-detail.util");
const winston_1 = require("winston");
const get_backtest_multi_method_dto_1 = require("../dto/get-backtest-multi-method.dto");
let BacktestMultiMethodController = class BacktestMultiMethodController {
    backtestMultiMethodService;
    logger;
    constructor(backtestMultiMethodService, logger) {
        this.backtestMultiMethodService = backtestMultiMethodService;
        this.logger = logger;
    }
    async getResult(body) {
        try {
            const result = await this.backtestMultiMethodService.getBacktestMultiMethodResult(body);
            return result;
        }
        catch (error) {
            const message = error?.message || 'Failed to generate result';
            this.logger.error('Failed to generate backtest multi method result', (0, log_detail_util_1.logDetail)({
                class: 'BacktestMultiMethodController',
                function: 'getResult',
                body,
                error: error.stack || message,
            }));
            throw new common_1.HttpException(message, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getPerformance(body) {
        try {
            const performance = await this.backtestMultiMethodService.getBacktestMultiMethodPerformance(body);
            return performance;
        }
        catch (error) {
            const message = error?.message || 'Failed to generate performance';
            this.logger.error('Failed to generate backtest multi method performance', (0, log_detail_util_1.logDetail)({
                class: 'BacktestMultiMethodController',
                function: 'getPerformance',
                body,
                error: error.stack || message,
            }));
            throw new common_1.HttpException(message, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async generateBoth(body) {
        try {
            const result = await this.backtestMultiMethodService.getBacktestMutilMethodBoth(body);
            return result;
        }
        catch (error) {
            const message = error?.message || 'Failed to generate both result and performance';
            this.logger.error('Failed to generate both', (0, log_detail_util_1.logDetail)({
                class: 'BacktestMultiMethodController',
                function: 'generateBoth',
                body,
                error: error.stack || message,
            }));
            throw new common_1.HttpException(message, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.BacktestMultiMethodController = BacktestMultiMethodController;
__decorate([
    (0, common_1.Post)('result'),
    (0, swagger_1.ApiOperation)({ summary: 'Generate Backtest Result' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_backtest_multi_method_dto_1.GetBacktestMultiMethodDto]),
    __metadata("design:returntype", Promise)
], BacktestMultiMethodController.prototype, "getResult", null);
__decorate([
    (0, common_1.Post)('performance'),
    (0, swagger_1.ApiOperation)({ summary: 'Generate Backtest Performance' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_backtest_multi_method_dto_1.GetBacktestMultiMethodDto]),
    __metadata("design:returntype", Promise)
], BacktestMultiMethodController.prototype, "getPerformance", null);
__decorate([
    (0, common_1.Post)('result-performance'),
    (0, swagger_1.ApiOperation)({ summary: 'Generate Result & Performance from Plan' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Combined result and performance from plan input',
        schema: {
            example: {
                result: [
                    {
                        methodId: 'abc123',
                        date: '2025-01-01T00:00:00.000Z',
                        status: 'profit',
                        openDate: '2025-01-02T00:00:00.000Z',
                        closedDate: '2025-01-03T00:00:00.000Z',
                        interval: '5',
                        symbol: 'BTCUSDT',
                        orderType: 'long',
                        entry: 10000,
                        stopLoss: 9000,
                        takeProfit: 11000,
                        stopPercent: 10,
                        profitPercent: 10,
                        expiryDate: '2025-01-04T00:00:00.000Z',
                    },
                ],
                performance: {
                    methodId: 'abc123',
                    fromDate: '2025-01-01T00:00:00.000Z',
                    endDate: '2025-06-01T00:00:00.000Z',
                    totalValidTrade: 23,
                    totalInvalidTrade: 3,
                    probability: 0.65,
                    maxOpenPosition: 3,
                    maxConsecutiveLoss: 2,
                },
            },
        },
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_backtest_multi_method_dto_1.GetBacktestMultiMethodDto]),
    __metadata("design:returntype", Promise)
], BacktestMultiMethodController.prototype, "generateBoth", null);
exports.BacktestMultiMethodController = BacktestMultiMethodController = __decorate([
    (0, swagger_1.ApiTags)('Backtest Multi Method'),
    (0, common_1.Controller)('backtest-multi-method'),
    __param(1, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [backtest_multi_method_service_1.BacktestMultiMethodService,
        winston_1.Logger])
], BacktestMultiMethodController);
//# sourceMappingURL=backtest-multi-method.controller.js.map