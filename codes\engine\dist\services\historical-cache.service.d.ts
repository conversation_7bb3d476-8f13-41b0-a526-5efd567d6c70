import { GetHistoricalDto } from 'src/dto/get-historical.dto';
import { Historical } from 'src/interface/historical.interface';
import { Logger } from 'winston';
export declare class HistoricalCacheService {
    private readonly logger;
    constructor(logger: Logger);
    private getFilePath;
    private writeWithRetry;
    private sanitizeJson;
    private readJsonFileSafe;
    validateCache(symbol: string, interval: string): Promise<boolean>;
    getHistorical(param: GetHistoricalDto): Promise<Historical[]>;
    insertHistorical(param: Historical[]): Promise<void>;
    deleteHistorical(param: {
        symbol: string;
        interval: string;
    }): Promise<void>;
    countHistorical(symbol: string, interval: string, start?: Date, end?: Date): Promise<number>;
    cleanupOldBackups(maxAgeDays?: number): Promise<void>;
}
