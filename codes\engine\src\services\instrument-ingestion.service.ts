import { Inject, Injectable } from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { logDetail } from 'src/util/log-detail.util';
import { Logger } from 'winston';
import { AdapterService } from './adapter.service';
import { InstrumentService } from './instrument.service';
import { Instrument } from 'src/interface/instrument.interface';
import { toMiliseconds } from 'src/util/to-milliseconds.util';
import { delay } from 'src/util/delay.util';
import { getSymbolsSlice } from 'src/util/get-symbols-slice.util';
import configurations from 'src/configurations';
import { MethodStatusService } from './method-status.service';

@Injectable()
export class InstrumentIngestionService {
  constructor(
    private readonly adapterService: AdapterService,
    private readonly instrumentService: InstrumentService,
    private readonly methodStatusService: MethodStatusService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) { }

  async ingest() {
    try {
      const disableCluster = configurations('DISABLE_CLUSTER');
      const engineMode = configurations('ENGINE_MODE');
      const symbols = await this.getSymbols(engineMode, disableCluster);
      const newData = await this.adapterService.fetchBybitInstrument();
      const filteredData = newData.filter(item => symbols.includes(item.symbol));
      const completedData = await this.ingestListedTime(filteredData);
      await this.instrumentService.insert(completedData);
    } catch (error) {
      this.logger.error(
        'Ingestion process failed',
        logDetail({
          class: 'InstrumentIngestionService',
          function: 'ingest',
          error,
        }),
      );
      throw new Error('Ingestion process failed');
    }
  }

  private async getSymbols(engineMode: string, disableCluster: string): Promise<string[]> {
    let symbols: string[];
    if (engineMode === 'worker') {
      symbols = await this.instrumentService.getSymbols();
    } else {
      const statuses = await this.methodStatusService.getAllMethodStatus();
      symbols = [...new Set(statuses.map(item => item.symbol))];
    }
    return disableCluster === 'false' ? getSymbolsSlice(symbols) : symbols;
  }

  private async ingestListedTime(newData: Instrument[]): Promise<Instrument[]> {
    const results: Instrument[] = [];
    const cpuAllocation = configurations('CPU_ALLOCATION') ?? configurations('TOTAL_CORE');
    const defaultLimit = configurations('DEFAULT_LIMIT');
    const oneDayMs = toMiliseconds('D');

    for (const data of newData) {
      const now = Date.now();
      for (let t = now; t > 0; t -= oneDayMs * defaultLimit) {
        const start = new Date(t - oneDayMs * defaultLimit);
        const end = new Date(t);

        const historical = await this.adapterService.fetchBybitHistorical({
          symbol: data.symbol,
          interval: 'D',
          start,
          end,
          limit: defaultLimit,
          sort: 'ASC',
        });

        if (historical.length) {
          data.listedTime = new Date(historical[0].date).getTime() + oneDayMs;
          await delay(1000 / (50 * cpuAllocation));
        } else {
          results.push(data);
          t = 0;
        }
      }
    }
    return results;
  }
}
