"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isInvalidOpenPrice = isInvalidOpenPrice;
function isInvalidOpenPrice(param) {
    const isInvalidTop = param.historical.open > param.entryPrice &&
        param.historical.open < param.targetPrice;
    const isInvalidBottom = param.historical.open < param.entryPrice &&
        param.historical.open > param.targetPrice;
    return isInvalidTop || isInvalidBottom;
}
//# sourceMappingURL=is-invalid-open-price.util.js.map