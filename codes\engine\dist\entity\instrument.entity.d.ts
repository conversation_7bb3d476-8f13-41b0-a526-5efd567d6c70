export declare class LeverageFilterEntity {
    id: string;
    minLeverage: number;
    maxLeverage: number;
    leverageStep: number;
}
export declare class PriceFilterEntity {
    id: string;
    minPrice: number;
    maxPrice: number;
    tickSize: number;
}
export declare class LotSizeFilterEntity {
    id: string;
    maxOrderQty: number;
    minOrderQty: number;
    qtyStep: number;
    postOnlyMaxOrderQty: number;
    maxMktOrderQty: number;
    minNotionalValue: number;
}
export declare class AuctionFeeInfoEntity {
    id: string;
    auctionFeeRate: string;
    takerFeeRate: string;
    makerFeeRate: string;
}
export declare class InstrumentEntity {
    id: string;
    symbol: string;
    launchTime: number;
    listedTime: number;
    priceScale: number;
    leverageFilter: LeverageFilterEntity;
    priceFilter: PriceFilterEntity;
    lotSizeFilter: LotSizeFilterEntity;
    fundingInterval: number;
    copyTrading: string;
    upperFundingRate: number;
    lowerFundingRate: number;
    auctionFeeInfo: AuctionFeeInfoEntity;
}
