import { Logger } from '@nestjs/common';
import { DataSource } from 'typeorm';
export declare class HealthCheckService {
    private readonly logger;
    private readonly dataSource;
    private static readonly TIMEOUT_MS;
    private readonly healthCheckUrls;
    constructor(logger: Logger, dataSource: DataSource);
    private checkDatabase;
    private checkUrl;
    check(): Promise<{
        timestamp: string;
        results: Record<string, 'reachable' | 'unreachable'>;
    }>;
}
