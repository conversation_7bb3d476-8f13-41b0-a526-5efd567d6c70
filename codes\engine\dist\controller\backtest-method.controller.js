"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BacktestMethodController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const nest_winston_1 = require("nest-winston");
const get_backtest_method_dto_1 = require("../dto/get-backtest-method.dto");
const backtest_method_service_1 = require("../services/backtest-method.service");
const log_detail_util_1 = require("../util/log-detail.util");
const winston_1 = require("winston");
let BacktestMethodController = class BacktestMethodController {
    backtestMethodService;
    logger;
    constructor(backtestMethodService, logger) {
        this.backtestMethodService = backtestMethodService;
        this.logger = logger;
    }
    async getResult(body) {
        try {
            body.start = new Date(body.start);
            body.end = new Date(body.end);
            const result = await this.backtestMethodService.getBacktestMethodResult(body);
            return result;
        }
        catch (error) {
            const message = error?.message || 'Failed to generate result';
            this.logger.error('Failed to generate backtest method result', (0, log_detail_util_1.logDetail)({
                class: 'BacktestMethodController',
                function: 'getResult',
                body,
                error: error.stack || message,
            }));
            throw new common_1.HttpException(message, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getPerformance(body) {
        try {
            body.start = new Date(body.start);
            body.end = new Date(body.end);
            const results = await this.backtestMethodService.getBacktestMethodResult(body);
            const performance = await this.backtestMethodService.getBacktestMethodPerformance(body, results);
            return performance;
        }
        catch (error) {
            const message = error?.message || 'Failed to generate performance';
            this.logger.error('Failed to generate performance', (0, log_detail_util_1.logDetail)({
                class: 'AppController',
                function: 'getPerformance',
                body,
                error: error.stack || message,
            }));
            throw new common_1.HttpException(message, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async generateBoth(body) {
        try {
            body.start = new Date(body.start);
            body.end = new Date(body.end);
            const results = await this.backtestMethodService.getBacktestMethodResult(body);
            const performance = await this.backtestMethodService.getBacktestMethodPerformance(body, results);
            return { result: results, performance };
        }
        catch (error) {
            const message = error?.message || 'Failed to generate performance';
            this.logger.error('Failed to generate performance', (0, log_detail_util_1.logDetail)({
                class: 'AppController',
                function: 'getPerformance',
                body,
                error: error.stack || message,
            }));
            throw new common_1.HttpException(message, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.BacktestMethodController = BacktestMethodController;
__decorate([
    (0, common_1.Post)('result'),
    (0, swagger_1.ApiOperation)({ summary: 'Generate Backtest Method Result' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of generated backtest method results',
        isArray: true,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_backtest_method_dto_1.GetBacktestMethodDto]),
    __metadata("design:returntype", Promise)
], BacktestMethodController.prototype, "getResult", null);
__decorate([
    (0, common_1.Post)('performance'),
    (0, swagger_1.ApiOperation)({ summary: 'Generate Backtest Performance' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_backtest_method_dto_1.GetBacktestMethodDto]),
    __metadata("design:returntype", Promise)
], BacktestMethodController.prototype, "getPerformance", null);
__decorate([
    (0, common_1.Post)('result-performance'),
    (0, swagger_1.ApiOperation)({ summary: 'Generate Result & Performance from Plan' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Combined result and performance from plan input',
        schema: {
            example: {
                result: [
                    {
                        methodId: 'abc123',
                        date: '2025-01-01T00:00:00.000Z',
                        status: 'profit',
                        openDate: '2025-01-02T00:00:00.000Z',
                        closedDate: '2025-01-03T00:00:00.000Z',
                    },
                ],
                performance: {
                    methodId: 'abc123',
                    fromDate: '2025-01-01T00:00:00.000Z',
                    endDate: '2025-06-01T00:00:00.000Z',
                    totalValidTrade: 23,
                    totalInvalidTrade: 3,
                    probability: 0.65,
                    maxOpenPosition: 3,
                    maxConsecutiveLoss: 2,
                },
            },
        },
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_backtest_method_dto_1.GetBacktestMethodDto]),
    __metadata("design:returntype", Promise)
], BacktestMethodController.prototype, "generateBoth", null);
exports.BacktestMethodController = BacktestMethodController = __decorate([
    (0, swagger_1.ApiTags)('Backtest Method'),
    (0, common_1.Controller)('backtest-method'),
    __param(1, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [backtest_method_service_1.BacktestMethodService,
        winston_1.Logger])
], BacktestMethodController);
//# sourceMappingURL=backtest-method.controller.js.map