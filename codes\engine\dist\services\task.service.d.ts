import { OnModuleInit } from '@nestjs/common';
import { HistoricalIngestionService } from './historical-ingestion.service';
import { InstrumentIngestionService } from './instrument-ingestion.service';
import { MethodStatusService } from './method-status.service';
import { MethodUpdaterService } from './method-updater.service';
import { MethodIngestionService } from './method-ingestion.service';
import { InstrumentService } from './instrument.service';
export declare class TaskService implements OnModuleInit {
    private readonly historicalIngestionService;
    private readonly methodStatusService;
    private readonly instrumentIngestionService;
    private readonly methodUpdaterService;
    private readonly methodIngestionService;
    private readonly instrumentService;
    private readonly logger;
    private isInitiated;
    constructor(historicalIngestionService: HistoricalIngestionService, methodStatusService: MethodStatusService, instrumentIngestionService: InstrumentIngestionService, methodUpdaterService: MethodUpdaterService, methodIngestionService: MethodIngestionService, instrumentService: InstrumentService);
    onModuleInit(): Promise<void>;
    handleInstrumentStatus(): Promise<void>;
    handleInterval(): Promise<void>;
}
