"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const swagger_1 = require("@nestjs/swagger");
const cluster_1 = __importDefault(require("cluster"));
const os_1 = __importDefault(require("os"));
const configurations_1 = __importDefault(require("./configurations"));
const common_1 = require("@nestjs/common");
const nest_winston_1 = require("nest-winston");
const body_parser_1 = __importDefault(require("body-parser"));
const delay_util_1 = require("./util/delay.util");
async function startApp() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    app.useLogger(app.get(nest_winston_1.WINSTON_MODULE_NEST_PROVIDER));
    app.enableVersioning({
        type: common_1.VersioningType.URI,
        defaultVersion: '1',
    });
    app.use(body_parser_1.default.json({ limit: '10mb' }));
    app.use(body_parser_1.default.urlencoded({ limit: '10mb', extended: true }));
    app.useGlobalPipes(new common_1.ValidationPipe({
        transform: true,
        whitelist: true,
        forbidNonWhitelisted: true,
    }));
    const swaggerConfig = new swagger_1.DocumentBuilder()
        .setTitle((0, configurations_1.default)('APP_NAME'))
        .setVersion('1.0')
        .addBearerAuth()
        .build();
    if ((0, configurations_1.default)('NODE_ENV') === 'development') {
        const document = swagger_1.SwaggerModule.createDocument(app, swaggerConfig);
        swagger_1.SwaggerModule.setup('', app, document);
    }
    const port = (0, configurations_1.default)('PORT') || 3000;
    await app.listen(port);
    console.log(`Application is running on port ${port}`);
}
async function startCluster() {
    const allocatedCore = (0, configurations_1.default)('CPU_ALLOCATION')
        ? Math.min(os_1.default.cpus().length, (0, configurations_1.default)('CPU_ALLOCATION'))
        : os_1.default.cpus().length;
    if (cluster_1.default.isPrimary) {
        console.log(`Cluster started with ${allocatedCore} worker(s)`);
        for (let i = 0; i < allocatedCore; i++) {
            cluster_1.default.fork({
                CORE_NUMBER: i,
            });
        }
        cluster_1.default.on('exit', (worker) => {
            console.log(`Worker with ID ${worker.process.pid} has exited.`);
            console.log('Creating a new worker...');
            const CORE_NUMBER = process.env.CORE_NUMBER;
            cluster_1.default.fork({
                CORE_NUMBER,
            });
        });
    }
    else {
        let coreNumber = 0;
        const workerId = cluster_1.default.worker?.id || 0;
        for (let i = -(workerId + 1); i < allocatedCore; i += allocatedCore) {
            coreNumber = i;
        }
        console.log(`Worker ${coreNumber} with ID ${process.pid} has started.`);
        await (0, delay_util_1.delay)(Math.round(1000 / allocatedCore));
        await startApp();
    }
}
if ((0, configurations_1.default)('DISABLE_CLUSTER') === 'true') {
    startApp();
}
else {
    startCluster();
}
//# sourceMappingURL=main.js.map