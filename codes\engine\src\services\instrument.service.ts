import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import configurations from 'src/configurations';
import { GetInstrumentDto } from 'src/dto/get-instrument.dto';
import { InstrumentEntity } from 'src/entity/instrument.entity';
import { Instrument } from 'src/interface/instrument.interface';
import { logDetail } from 'src/util/log-detail.util';
import { In, Repository } from 'typeorm';
import { Logger } from 'winston';

@Injectable()
export class InstrumentService {
  constructor(
    @InjectRepository(InstrumentEntity)
    private readonly InstrumentsRepository: Repository<InstrumentEntity>,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) { }

  async getInstrument(param: GetInstrumentDto): Promise<InstrumentEntity[]> {
    try {
      const queryBuilder = this.InstrumentsRepository.createQueryBuilder(
        'instrument',
      )
        .leftJoinAndSelect('instrument.leverageFilter', 'leverageFilter')
        .leftJoinAndSelect('instrument.priceFilter', 'priceFilter')
        .leftJoinAndSelect('instrument.lotSizeFilter', 'lotSizeFilter');

      // Add conditions based on provided parameters
      if (param.symbol) {
        queryBuilder.andWhere('instrument.symbol = :symbol', {
          symbol: param.symbol,
        });
      }
      return await queryBuilder.getMany();
    } catch (err) {
      this.logger.error(
        'Failed to read instrument data',
        logDetail({
          class: 'AppService',
          function: 'read',
          error: err,
          param,
        }),
      );
      throw new Error(`Failed to read instrument data`);
    }
  }

  async insert(param: Instrument[]): Promise<InstrumentEntity[]> {
    try {
      const symbols = param.map((p) => p.symbol);
      const existingRecords = await this.InstrumentsRepository.find({
        where: { symbol: In(symbols) },
      });
      const existingMap = new Map(existingRecords.map((r) => [r.symbol, r]));
      const mergedRecords = param.map((record) => {
        const existing = existingMap.get(record.symbol);
        if (existing) {
          return {
            ...existing,
            ...record,
            auctionFeeInfo: {
              ...(existing.auctionFeeInfo ?? {}),
              ...(record.auctionFeeInfo ?? {}),
            },
            leverageFilter: {
              ...(existing.leverageFilter ?? {}),
              ...(record.leverageFilter ?? {}),
            },
            priceFilter: {
              ...(existing.priceFilter ?? {}),
              ...(record.priceFilter ?? {}),
            },
            lotSizeFilter: {
              ...(existing.lotSizeFilter ?? {}),
              ...(record.lotSizeFilter ?? {}),
            },
          };
        } else {
          return record;
        }
      });

      return await this.InstrumentsRepository.save(mergedRecords);
    } catch (err) {
      this.logger.error(
        'Failed to upsert instrument data',
        logDetail({
          class: 'AppService',
          function: 'insert',
          error: err,
          param,
        }),
      );
      throw new Error('Failed to upsert instrument data');
    }
  }

  async getSymbols(): Promise<string[]> {
    try {
      const launchTimeThreshold =
        new Date().getTime() -
        configurations('SYMBOL_LAUNCH_TIME_THRESHOLD_DAYS') *
        24 *
        60 *
        60 *
        1000;
      const queryBuilder =
        this.InstrumentsRepository.createQueryBuilder('instrument');
      queryBuilder.andWhere('instrument.launchTime < :launchTimeThreshold', {
        launchTimeThreshold,
      });
      const symbols = await queryBuilder.select('instrument.symbol').getMany();
      return symbols.map((s) => s.symbol);
    } catch (err) {
      this.logger.error(
        'Failed to read instrument data',
        logDetail({
          class: 'AppService',
          function: 'getSymbols',
          error: err,
        }),
      );
      throw new Error(`Failed to read instrument data`);
    }
  }
}
