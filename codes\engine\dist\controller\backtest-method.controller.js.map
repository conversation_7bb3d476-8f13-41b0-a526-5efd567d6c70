{"version": 3, "file": "backtest-method.controller.js", "sourceRoot": "", "sources": ["../../src/controller/backtest-method.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAOwB;AACxB,6CAAqE;AACrE,+CAAuD;AACvD,4EAAuE;AAGvE,iFAA6E;AAC7E,6DAAqD;AACrD,qCAAiC;AAI1B,IAAM,wBAAwB,GAA9B,MAAM,wBAAwB;IAEhB;IACiC;IAFpD,YACmB,qBAA4C,EACX,MAAc;QAD/C,0BAAqB,GAArB,qBAAqB,CAAuB;QACX,WAAM,GAAN,MAAM,CAAQ;IAC/D,CAAC;IASE,AAAN,KAAK,CAAC,SAAS,CAAS,IAA0B;QAChD,IAAI,CAAC;YACH,IAAI,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClC,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC9B,MAAM,MAAM,GACV,MAAM,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;YACjE,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,OAAO,GAAG,KAAK,EAAE,OAAO,IAAI,2BAA2B,CAAC;YAE9D,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,2CAA2C,EAC3C,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,0BAA0B;gBACjC,QAAQ,EAAE,WAAW;gBACrB,IAAI;gBACJ,KAAK,EAAE,KAAK,CAAC,KAAK,IAAI,OAAO;aAC9B,CAAC,CACH,CAAC;YAEF,MAAM,IAAI,sBAAa,CAAC,OAAO,EAAE,mBAAU,CAAC,qBAAqB,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAIK,AAAN,KAAK,CAAC,cAAc,CACV,IAA0B;QAElC,IAAI,CAAC;YACH,IAAI,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClC,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC9B,MAAM,OAAO,GACX,MAAM,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;YACjE,MAAM,WAAW,GACf,MAAM,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAC3D,IAAI,EACJ,OAAO,CACR,CAAC;YACJ,OAAO,WAAW,CAAC;QACrB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,OAAO,GAAG,KAAK,EAAE,OAAO,IAAI,gCAAgC,CAAC;YAEnE,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,gCAAgC,EAChC,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,eAAe;gBACtB,QAAQ,EAAE,gBAAgB;gBAC1B,IAAI;gBACJ,KAAK,EAAE,KAAK,CAAC,KAAK,IAAI,OAAO;aAC9B,CAAC,CACH,CAAC;YAEF,MAAM,IAAI,sBAAa,CAAC,OAAO,EAAE,mBAAU,CAAC,qBAAqB,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IA+BK,AAAN,KAAK,CAAC,YAAY,CAAS,IAA0B;QAInD,IAAI,CAAC;YACH,IAAI,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAClC,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC9B,MAAM,OAAO,GACX,MAAM,IAAI,CAAC,qBAAqB,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;YACjE,MAAM,WAAW,GACf,MAAM,IAAI,CAAC,qBAAqB,CAAC,4BAA4B,CAC3D,IAAI,EACJ,OAAO,CACR,CAAC;YACJ,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;QAC1C,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,OAAO,GAAG,KAAK,EAAE,OAAO,IAAI,gCAAgC,CAAC;YAEnE,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,gCAAgC,EAChC,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,eAAe;gBACtB,QAAQ,EAAE,gBAAgB;gBAC1B,IAAI;gBACJ,KAAK,EAAE,KAAK,CAAC,KAAK,IAAI,OAAO;aAC9B,CAAC,CACH,CAAC;YAEF,MAAM,IAAI,sBAAa,CAAC,OAAO,EAAE,mBAAU,CAAC,qBAAqB,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;CACF,CAAA;AAlIY,4DAAwB;AAa7B;IAPL,IAAA,aAAI,EAAC,QAAQ,CAAC;IACd,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,iCAAiC,EAAE,CAAC;IAC5D,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,2CAA2C;QACxD,OAAO,EAAE,IAAI;KACd,CAAC;IACe,WAAA,IAAA,aAAI,GAAE,CAAA;;qCAAO,8CAAoB;;yDAsBjD;AAIK;IAFL,IAAA,aAAI,EAAC,aAAa,CAAC;IACnB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,+BAA+B,EAAE,CAAC;IAExD,WAAA,IAAA,aAAI,GAAE,CAAA;;qCAAO,8CAAoB;;8DA4BnC;AA+BK;IA7BL,IAAA,aAAI,EAAC,oBAAoB,CAAC;IAC1B,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,yCAAyC,EAAE,CAAC;IACpE,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,iDAAiD;QAC9D,MAAM,EAAE;YACN,OAAO,EAAE;gBACP,MAAM,EAAE;oBACN;wBACE,QAAQ,EAAE,QAAQ;wBAClB,IAAI,EAAE,0BAA0B;wBAChC,MAAM,EAAE,QAAQ;wBAChB,QAAQ,EAAE,0BAA0B;wBACpC,UAAU,EAAE,0BAA0B;qBACvC;iBACF;gBACD,WAAW,EAAE;oBACX,QAAQ,EAAE,QAAQ;oBAClB,QAAQ,EAAE,0BAA0B;oBACpC,OAAO,EAAE,0BAA0B;oBACnC,eAAe,EAAE,EAAE;oBACnB,iBAAiB,EAAE,CAAC;oBACpB,WAAW,EAAE,IAAI;oBACjB,eAAe,EAAE,CAAC;oBAClB,kBAAkB,EAAE,CAAC;iBACtB;aACF;SACF;KACF,CAAC;IACkB,WAAA,IAAA,aAAI,GAAE,CAAA;;qCAAO,8CAAoB;;4DA8BpD;mCAjIU,wBAAwB;IAFpC,IAAA,iBAAO,EAAC,iBAAiB,CAAC;IAC1B,IAAA,mBAAU,EAAC,iBAAiB,CAAC;IAIzB,WAAA,IAAA,eAAM,EAAC,sCAAuB,CAAC,CAAA;qCADQ,+CAAqB;QACH,gBAAM;GAHvD,wBAAwB,CAkIpC"}