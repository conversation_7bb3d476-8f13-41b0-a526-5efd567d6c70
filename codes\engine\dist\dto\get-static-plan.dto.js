"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetStaticPlanDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const configurations_1 = __importDefault(require("../configurations"));
const get_pattern_dto_1 = require("./get-pattern.dto");
class GetStaticPlanDto extends get_pattern_dto_1.GetPatternDto {
    methodId;
    orderType;
    validityPeriod;
    entryPercentByClose;
    riskPercent;
    rewardPercent;
}
exports.GetStaticPlanDto = GetStaticPlanDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Method ID',
    }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GetStaticPlanDto.prototype, "methodId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Order type for backtesting (e.g., long, short)',
        example: 'long',
        enum: (0, configurations_1.default)('ORDER_TYPES'),
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], GetStaticPlanDto.prototype, "orderType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of samples to validate plan validity',
        example: 100,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], GetStaticPlanDto.prototype, "validityPeriod", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Percentage of entry by close per trade',
        example: -0.5,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], GetStaticPlanDto.prototype, "entryPercentByClose", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Percentage of risk per trade',
        example: -1,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], GetStaticPlanDto.prototype, "riskPercent", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Percentage of reward per trade',
        example: 2,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], GetStaticPlanDto.prototype, "rewardPercent", void 0);
//# sourceMappingURL=get-static-plan.dto.js.map