import { GetResultArrayDto } from 'src/dto/get-result-array.dto';
import { BacktestPerformance } from 'src/interface/backtest-performance.interface';
import { BacktestService } from 'src/services/backtest.service';
import { MethodResult } from 'src/interface/method-result.interface';
import { Logger } from 'winston';
export declare class BacktestController {
    private readonly backtestService;
    private readonly logger;
    constructor(backtestService: BacktestService, logger: Logger);
    getResult(body: GetResultArrayDto): Promise<MethodResult[]>;
    getPerformance(body: GetResultArrayDto): Promise<BacktestPerformance>;
    generateBoth(body: GetResultArrayDto): Promise<{
        result: MethodResult[];
        performance: BacktestPerformance;
    }>;
}
