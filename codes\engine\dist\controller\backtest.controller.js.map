{"version": 3, "file": "backtest.controller.js", "sourceRoot": "", "sources": ["../../src/controller/backtest.controller.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAOwB;AACxB,6CAAqE;AACrE,+CAAuD;AACvD,sEAAiE;AAEjE,mEAAgE;AAChE,6DAAqD;AAErD,qCAAiC;AAI1B,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;IAEV;IACiC;IAFpD,YACmB,eAAgC,EACC,MAAc;QAD/C,oBAAe,GAAf,eAAe,CAAiB;QACC,WAAM,GAAN,MAAM,CAAQ;IAC/D,CAAC;IASE,AAAN,KAAK,CAAC,SAAS,CAAS,IAAuB;QAC7C,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1E,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,OAAO,GAAG,KAAK,EAAE,OAAO,IAAI,2BAA2B,CAAC;YAE9D,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,2BAA2B,EAC3B,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,eAAe;gBACtB,QAAQ,EAAE,WAAW;gBACrB,IAAI;gBACJ,KAAK,EAAE,KAAK,CAAC,KAAK,IAAI,OAAO;aAC9B,CAAC,CACH,CAAC;YAEF,MAAM,IAAI,sBAAa,CAAC,OAAO,EAAE,mBAAU,CAAC,qBAAqB,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAoBK,AAAN,KAAK,CAAC,cAAc,CACV,IAAuB;QAE/B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1E,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACtE,OAAO,WAAW,CAAC;QACrB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,OAAO,GAAG,KAAK,EAAE,OAAO,IAAI,gCAAgC,CAAC;YAEnE,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,gCAAgC,EAChC,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,eAAe;gBACtB,QAAQ,EAAE,gBAAgB;gBAC1B,IAAI;gBACJ,KAAK,EAAE,KAAK,CAAC,KAAK,IAAI,OAAO;aAC9B,CAAC,CACH,CAAC;YAEF,MAAM,IAAI,sBAAa,CAAC,OAAO,EAAE,mBAAU,CAAC,qBAAqB,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IA+BK,AAAN,KAAK,CAAC,YAAY,CAAS,IAAuB;QAIhD,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1E,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACtE,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC;QACjC,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,OAAO,GACX,KAAK,EAAE,OAAO,IAAI,gDAAgD,CAAC;YAErE,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,yBAAyB,EACzB,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,eAAe;gBACtB,QAAQ,EAAE,cAAc;gBACxB,IAAI;gBACJ,KAAK,EAAE,KAAK,CAAC,KAAK,IAAI,OAAO;aAC9B,CAAC,CACH,CAAC;YAEF,MAAM,IAAI,sBAAa,CAAC,OAAO,EAAE,mBAAU,CAAC,qBAAqB,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;CACF,CAAA;AAlIY,gDAAkB;AAavB;IAPL,IAAA,aAAI,EAAC,QAAQ,CAAC;IACd,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,0BAA0B,EAAE,CAAC;IACrD,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,kCAAkC;QAC/C,OAAO,EAAE,IAAI;KACd,CAAC;IACe,WAAA,IAAA,aAAI,GAAE,CAAA;;qCAAO,wCAAiB;;mDAmB9C;AAoBK;IAlBL,IAAA,aAAI,EAAC,aAAa,CAAC;IACnB,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,+BAA+B,EAAE,CAAC;IAC1D,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,6BAA6B;QAC1C,MAAM,EAAE;YACN,OAAO,EAAE;gBACP,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,0BAA0B;gBACpC,OAAO,EAAE,0BAA0B;gBACnC,eAAe,EAAE,EAAE;gBACnB,iBAAiB,EAAE,CAAC;gBACpB,WAAW,EAAE,IAAI;gBACjB,eAAe,EAAE,CAAC;gBAClB,kBAAkB,EAAE,CAAC;aACtB;SACF;KACF,CAAC;IAEC,WAAA,IAAA,aAAI,GAAE,CAAA;;qCAAO,wCAAiB;;wDAqBhC;AA+BK;IA7BL,IAAA,aAAI,EAAC,oBAAoB,CAAC;IAC1B,IAAA,sBAAY,EAAC,EAAE,OAAO,EAAE,yCAAyC,EAAE,CAAC;IACpE,IAAA,qBAAW,EAAC;QACX,MAAM,EAAE,GAAG;QACX,WAAW,EAAE,iDAAiD;QAC9D,MAAM,EAAE;YACN,OAAO,EAAE;gBACP,MAAM,EAAE;oBACN;wBACE,QAAQ,EAAE,QAAQ;wBAClB,IAAI,EAAE,0BAA0B;wBAChC,MAAM,EAAE,QAAQ;wBAChB,QAAQ,EAAE,0BAA0B;wBACpC,UAAU,EAAE,0BAA0B;qBACvC;iBACF;gBACD,WAAW,EAAE;oBACX,QAAQ,EAAE,QAAQ;oBAClB,QAAQ,EAAE,0BAA0B;oBACpC,OAAO,EAAE,0BAA0B;oBACnC,eAAe,EAAE,EAAE;oBACnB,iBAAiB,EAAE,CAAC;oBACpB,WAAW,EAAE,IAAI;oBACjB,eAAe,EAAE,CAAC;oBAClB,kBAAkB,EAAE,CAAC;iBACtB;aACF;SACF;KACF,CAAC;IACkB,WAAA,IAAA,aAAI,GAAE,CAAA;;qCAAO,wCAAiB;;sDAwBjD;6BAjIU,kBAAkB;IAF9B,IAAA,iBAAO,EAAC,UAAU,CAAC;IACnB,IAAA,mBAAU,EAAC,UAAU,CAAC;IAIlB,WAAA,IAAA,eAAM,EAAC,sCAAuB,CAAC,CAAA;qCADE,kCAAe;QACS,gBAAM;GAHvD,kBAAkB,CAkI9B"}