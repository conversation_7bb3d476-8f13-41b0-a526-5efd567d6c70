"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MethodParamEntity = void 0;
const typeorm_1 = require("typeorm");
let MethodParamEntity = class MethodParamEntity {
    methodId;
    symbol;
    interval;
    start;
    end;
    limit;
    sort;
    trend;
    patternType;
    pattern;
    orderType;
    validityPeriod;
    entryPercentByClose;
    riskPercent;
    rewardPercent;
    lookbackPeriod;
    methodType;
    enableOptimization;
};
exports.MethodParamEntity = MethodParamEntity;
__decorate([
    (0, typeorm_1.PrimaryColumn)({ type: 'varchar', nullable: false }),
    __metadata("design:type", String)
], MethodParamEntity.prototype, "methodId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 20 }),
    __metadata("design:type", String)
], MethodParamEntity.prototype, "symbol", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 10 }),
    __metadata("design:type", String)
], MethodParamEntity.prototype, "interval", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp' }),
    __metadata("design:type", Date)
], MethodParamEntity.prototype, "start", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp' }),
    __metadata("design:type", Date)
], MethodParamEntity.prototype, "end", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'bigint' }),
    __metadata("design:type", Number)
], MethodParamEntity.prototype, "limit", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 10 }),
    __metadata("design:type", String)
], MethodParamEntity.prototype, "sort", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 10 }),
    __metadata("design:type", String)
], MethodParamEntity.prototype, "trend", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 30 }),
    __metadata("design:type", String)
], MethodParamEntity.prototype, "patternType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 50 }),
    __metadata("design:type", String)
], MethodParamEntity.prototype, "pattern", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 10 }),
    __metadata("design:type", String)
], MethodParamEntity.prototype, "orderType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int' }),
    __metadata("design:type", Number)
], MethodParamEntity.prototype, "validityPeriod", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'float' }),
    __metadata("design:type", Number)
], MethodParamEntity.prototype, "entryPercentByClose", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'float' }),
    __metadata("design:type", Number)
], MethodParamEntity.prototype, "riskPercent", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'float' }),
    __metadata("design:type", Number)
], MethodParamEntity.prototype, "rewardPercent", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int' }),
    __metadata("design:type", Number)
], MethodParamEntity.prototype, "lookbackPeriod", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 10 }),
    __metadata("design:type", String)
], MethodParamEntity.prototype, "methodType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean' }),
    __metadata("design:type", Boolean)
], MethodParamEntity.prototype, "enableOptimization", void 0);
exports.MethodParamEntity = MethodParamEntity = __decorate([
    (0, typeorm_1.Entity)('method-param'),
    (0, typeorm_1.Index)(['symbol', 'interval'])
], MethodParamEntity);
//# sourceMappingURL=method-param.entity.js.map