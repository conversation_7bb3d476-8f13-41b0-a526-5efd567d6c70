import configurations from "src/configurations";

// Helper: slicing & shuffling symbols
export function getSymbolsSlice(symbols: string[]) {
    const totalCore = configurations('CPU_ALLOCATION') ?? configurations('TOTAL_CORE');
    const coreNumber = configurations('CORE_NUMBER');
    const totalServer = configurations('TOTAL_SERVER');
    const serverNumber = configurations('SERVER_NUMBER');
    const totalSymbolPerServer = symbols.length / totalServer;
    const totalSymbolPerCore = totalSymbolPerServer / totalCore;
    const startSymbol = Math.floor(
        serverNumber * totalSymbolPerCore + coreNumber * totalSymbolPerCore,
    );
    const endSymbol = Math.ceil(
        serverNumber * totalSymbolPerCore +
        (coreNumber + 1) * totalSymbolPerCore,
    );
    return symbols.slice(startSymbol, endSymbol)
};