"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateResult = generateResult;
const to_milliseconds_util_1 = require("./to-milliseconds.util");
const is_match_target_price_util_1 = require("./is-match-target-price.util");
const is_invalid_open_price_util_1 = require("./is-invalid-open-price.util");
function generateResult(param, historicalExecution) {
    const startDate = new Date(param.date.getTime() + (0, to_milliseconds_util_1.toMiliseconds)(param.interval));
    const historicalExecutionSample = historicalExecution.filter((historicalItem) => historicalItem.date.getTime() >= startDate.getTime());
    const result = {
        methodId: param.methodId ?? '',
        orderType: param.orderType,
        date: param.date,
        symbol: param.symbol,
        entry: param.entry,
        stopLoss: param.stopLoss,
        takeProfit: param.takeProfit,
        stopPercent: param.stopPercent,
        profitPercent: param.profitPercent,
        expiryDate: param.expiryDate,
        interval: param.interval,
        status: 'pending',
    };
    for (const historical of historicalExecutionSample) {
        if (result.status === 'pending' &&
            historical.date.getTime() >= param.expiryDate.getTime()) {
            result.status = 'expired';
            break;
        }
        if (result.status === 'pending' &&
            (0, is_match_target_price_util_1.isMatchTargetPrice)(param.entry, historical)) {
            result.status = 'open';
            result.openDate = historical.date;
        }
        if (result.status === 'open' &&
            (0, is_match_target_price_util_1.isMatchTargetPrice)(param.stopLoss, historical) &&
            (0, is_match_target_price_util_1.isMatchTargetPrice)(param.takeProfit, historical)) {
            result.status = 'invalid';
            result.closedDate = historical.date;
            break;
        }
        if (result.status === 'open' &&
            result.openDate === historical.date &&
            (0, is_match_target_price_util_1.isMatchTargetPrice)(param.stopLoss, historical) &&
            (0, is_match_target_price_util_1.isMatchTargetPrice)(param.entry, historical) &&
            (0, is_invalid_open_price_util_1.isInvalidOpenPrice)({
                entryPrice: param.entry,
                targetPrice: param.stopLoss,
                historical,
            })) {
            result.status = 'invalid';
            result.closedDate = historical.date;
            break;
        }
        if (result.status === 'open' &&
            result.openDate === historical.date &&
            (0, is_match_target_price_util_1.isMatchTargetPrice)(param.takeProfit, historical) &&
            (0, is_match_target_price_util_1.isMatchTargetPrice)(param.entry, historical) &&
            (0, is_invalid_open_price_util_1.isInvalidOpenPrice)({
                entryPrice: param.entry,
                targetPrice: param.takeProfit,
                historical,
            })) {
            result.status = 'invalid';
            result.closedDate = historical.date;
            break;
        }
        if (result.status === 'open' &&
            (0, is_match_target_price_util_1.isMatchTargetPrice)(param.stopLoss, historical)) {
            result.status = 'loss';
            result.closedDate = historical.date;
            break;
        }
        if (result.status === 'open' &&
            (0, is_match_target_price_util_1.isMatchTargetPrice)(param.takeProfit, historical)) {
            result.status = 'profit';
            result.closedDate = historical.date;
            break;
        }
    }
    return result;
}
//# sourceMappingURL=generate-result.util.js.map