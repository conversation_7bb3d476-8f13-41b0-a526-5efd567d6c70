import { MethodParamEntity } from 'src/entity/method-param.entity';
import { MethodResultEntity } from 'src/entity/method-result.entity';
import { MethodService } from 'src/services/method.service';
import { MethodPerformanceEntity } from 'src/entity/method-performance.entity';
import { Logger } from 'winston';
import { GetMethodDto } from 'src/dto/get-method.dto';
export declare class MethodController {
    private readonly methodService;
    private readonly logger;
    constructor(methodService: MethodService, logger: Logger);
    getParam(body: GetMethodDto): Promise<MethodParamEntity[]>;
    getResult(body: GetMethodDto): Promise<MethodResultEntity[]>;
    getPerformance(body: GetMethodDto): Promise<MethodPerformanceEntity[]>;
}
