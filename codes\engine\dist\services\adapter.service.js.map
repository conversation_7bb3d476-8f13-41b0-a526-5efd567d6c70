{"version": 3, "file": "adapter.service.js", "sourceRoot": "", "sources": ["../../src/services/adapter.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,+CAAuD;AACvD,qCAAiC;AACjC,kDAA0C;AAC1C,uEAAgD;AAChD,6DAAqD;AAO9C,IAAM,cAAc,GAApB,MAAM,cAAc;IAE2B;IADpD,YACoD,MAAc;QAAd,WAAM,GAAN,MAAM,CAAQ;IAC9D,CAAC;IAEL,KAAK,CAAC,oBAAoB,CAAC,KAAuB;QAChD,MAAM,cAAc,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;QAC7C,MAAM,YAAY,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;QAEzC,MAAM,GAAG,GACP,GAAG,IAAA,wBAAc,EAAC,gBAAgB,CAAC,kBAAkB;YACrD,2BAA2B,KAAK,CAAC,MAAM,EAAE;YACzC,aAAa,KAAK,CAAC,QAAQ,UAAU,cAAc,QAAQ,YAAY,UAAU,KAAK,CAAC,KAAK,EAAE,CAAC;QAEjG,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YAEzD,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC;YAC5C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,2CAA2C,EAC3C,IAAA,2BAAS,EAAC;oBACR,KAAK,EAAE,YAAY;oBACnB,QAAQ,EAAE,sBAAsB;oBAChC,GAAG;oBACH,KAAK;iBACN,CAAC,CACH,CAAC;gBACF,OAAO,EAAE,CAAC;YACZ,CAAC;YAED,MAAM,WAAW,GAAiB,OAAO;iBACtC,GAAG,CAAC,CAAC,IAAc,EAAE,EAAE,CAAC,CAAC;gBACxB,MAAM,EAAE,KAAK,CAAC,MAAM;gBACpB,IAAI,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/B,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACzB,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACzB,GAAG,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACxB,KAAK,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC1B,MAAM,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC3B,QAAQ,EAAE,KAAK,CAAC,QAAQ;aACzB,CAAC,CAAC;iBACF,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBACb,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;oBAC1B,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC7C,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC7C,CAAC;YACH,CAAC,CAAC,CAAC;YAEL,OAAO,WAAW,CAAC;QACrB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,MAAM,KAAK,GAAG,GAAiB,CAAC;YAEhC,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,iCAAiC,EACjC,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,YAAY;gBACnB,QAAQ,EAAE,sBAAsB;gBAChC,GAAG;gBACH,KAAK;gBACL,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,KAAK;aAC7C,CAAC,CACH,CAAC;YAEF,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,KAAwB;QACjD,IAAI,GAAG,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,GAAG;gBACD,GAAG,IAAA,wBAAc,EAAC,gBAAgB,CAAC,6BAA6B;oBAChE,kBAAkB;oBAClB,UAAU,IAAA,wBAAc,EAAC,eAAe,CAAC,EAAE,CAAC;QAChD,CAAC;aAAM,CAAC;YACN,GAAG;gBACD,GAAG,IAAA,wBAAc,EAAC,gBAAgB,CAAC,6BAA6B;oBAChE,2BAA2B,KAAK,CAAC,MAAM,EAAE;oBACzC,UAAU,IAAA,wBAAc,EAAC,eAAe,CAAC,EAAE,CAAC;QAChD,CAAC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YAEzD,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC;YAC5C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC5B,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,2CAA2C,EAC3C,IAAA,2BAAS,EAAC;oBACR,KAAK,EAAE,YAAY;oBACnB,QAAQ,EAAE,sBAAsB;oBAChC,GAAG;oBACH,KAAK;iBACN,CAAC,CACH,CAAC;gBACF,OAAO,EAAE,CAAC;YACZ,CAAC;YACD,MAAM,UAAU,GAAiB,OAAO,CAAC,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,CAAC;gBAC3D,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;gBAEjC,UAAU,EAAE,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBACvE,UAAU,EAAE,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBAEvE,UAAU,EAAE,IAAI,CAAC,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBAEvE,cAAc,EAAE;oBACd,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,WAAW,IAAI,CAAC,CAAC;oBAC1D,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,WAAW,IAAI,CAAC,CAAC;oBAC1D,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,YAAY,IAAI,CAAC,CAAC;iBAC7D;gBAED,WAAW,EAAE;oBACX,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,IAAI,CAAC,CAAC;oBACjD,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,IAAI,CAAC,CAAC;oBACjD,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,IAAI,CAAC,CAAC;iBAClD;gBAED,aAAa,EAAE;oBACb,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,WAAW,IAAI,CAAC,CAAC;oBACzD,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,WAAW,IAAI,CAAC,CAAC;oBACzD,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,IAAI,CAAC,CAAC;oBACjD,mBAAmB,EAAE,MAAM,CACzB,IAAI,CAAC,aAAa,EAAE,mBAAmB,IAAI,CAAC,CAC7C;oBACD,cAAc,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,cAAc,IAAI,CAAC,CAAC;oBAC/D,gBAAgB,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,gBAAgB,IAAI,CAAC,CAAC;iBACpE;gBAED,eAAe,EACb,IAAI,CAAC,eAAe,KAAK,IAAI,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS;oBACjE,CAAC,CAAC,CAAC;oBACH,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC;gBAElC,gBAAgB,EAAE,MAAM,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAC;gBACpD,gBAAgB,EAAE,MAAM,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAC;gBACpD,cAAc,EAAE;oBACd,cAAc,EAAE,IAAA,wBAAc,EAAC,wBAAwB,CAAC;oBACxD,YAAY,EAAE,IAAA,wBAAc,EAAC,sBAAsB,CAAC;oBACpD,YAAY,EAAE,IAAA,wBAAc,EAAC,sBAAsB,CAAC;iBACrD;aACF,CAAC,CAAC,CAAC;YAEJ,OAAO,UAAU,CAAC;QACpB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,MAAM,KAAK,GAAG,GAAiB,CAAC;YAEhC,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,iCAAiC,EACjC,IAAA,2BAAS,EAAC;gBACR,KAAK,EAAE,YAAY;gBACnB,QAAQ,EAAE,sBAAsB;gBAChC,GAAG;gBACH,KAAK;gBACL,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,KAAK;aAC7C,CAAC,CACH,CAAC;YAEF,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;CACF,CAAA;AAlKY,wCAAc;yBAAd,cAAc;IAD1B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,eAAM,EAAC,sCAAuB,CAAC,CAAA;qCAA0B,gBAAM;GAFvD,cAAc,CAkK1B"}