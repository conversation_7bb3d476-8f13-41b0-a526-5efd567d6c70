import { GetInstrumentDto } from 'src/dto/get-instrument.dto';
import { InstrumentEntity } from 'src/entity/instrument.entity';
import { InstrumentService } from 'src/services/instrument.service';
import { Logger } from 'winston';
export declare class InstrumentController {
    private readonly instrumentService;
    private readonly logger;
    constructor(instrumentService: InstrumentService, logger: Logger);
    get(body: GetInstrumentDto): Promise<InstrumentEntity[]>;
}
