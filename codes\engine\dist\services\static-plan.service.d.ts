import { Logger } from 'winston';
import { GetStaticPlanDto } from 'src/dto/get-static-plan.dto';
import { Plan } from 'src/interface/plan.interface';
import { InstrumentService } from './instrument.service';
import { Pattern } from 'src/interface/pattern.interface';
import { Historical } from 'src/interface/historical.interface';
import { PatternService } from './pattern.service';
import { Instrument } from 'src/interface/instrument.interface';
import { HistoricalCacheService } from './historical-cache.service';
export declare class StaticPlanService {
    private readonly logger;
    private readonly instrumentService;
    private readonly historicalCacheService;
    private readonly patternService;
    constructor(logger: Logger, instrumentService: InstrumentService, historicalCacheService: HistoricalCacheService, patternService: PatternService);
    getPlan(param: GetStaticPlanDto, historical?: Historical[], patterns?: Pattern[], instrument?: Instrument[]): Promise<Plan[]>;
}
