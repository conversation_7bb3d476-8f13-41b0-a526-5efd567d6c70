import { Inject, Injectable } from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import axios, { AxiosError } from 'axios';
import configurations from 'src/configurations';
import { logDetail } from 'src/util/log-detail.util';
import { GetHistoricalDto } from 'src/dto/get-historical.dto';
import { GetInstrumentDto } from 'src/dto/get-instrument.dto';
import { Historical } from 'src/interface/historical.interface';
import { Instrument } from 'src/interface/instrument.interface';

@Injectable()
export class AdapterService {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) { }

  async fetchBybitHistorical(param: GetHistoricalDto): Promise<Historical[]> {
    const startTimestamp = param.start.getTime();
    const endTimestamp = param.end.getTime();

    const url =
      `${configurations('BYBIT_API_HOST')}/v5/market/kline` +
      `?category=linear&symbol=${param.symbol}` +
      `&interval=${param.interval}&start=${startTimestamp}&end=${endTimestamp}&limit=${param.limit}`;

    try {
      const response = await axios.get(url, { timeout: 5000 });

      const rawData = response.data?.result?.list;
      if (!Array.isArray(rawData)) {
        this.logger.warn(
          'Unexpected response format from Bybit API',
          logDetail({
            class: 'AppService',
            function: 'fetchBybitHistorical',
            url,
            param,
          }),
        );
        return [];
      }

      const historicals: Historical[] = rawData
        .map((item: string[]) => ({
          symbol: param.symbol,
          date: new Date(Number(item[0])),
          open: parseFloat(item[1]),
          high: parseFloat(item[2]),
          low: parseFloat(item[3]),
          close: parseFloat(item[4]),
          volume: parseFloat(item[5]),
          interval: param.interval,
        }))
        .sort((a, b) => {
          if (param.sort === 'DESC') {
            return b.date.getTime() - a.date.getTime();
          } else {
            return a.date.getTime() - b.date.getTime();
          }
        });

      return historicals;
    } catch (err) {
      const error = err as AxiosError;

      this.logger.error(
        `Failed to fetch historical data`,
        logDetail({
          class: 'AppService',
          function: 'fetchBybitHistorical',
          url,
          param,
          error: error.toJSON ? error.toJSON() : error,
        }),
      );

      return await this.fetchBybitHistorical(param);
    }
  }

  async fetchBybitInstrument(param?: GetInstrumentDto): Promise<Instrument[]> {
    let url = '';
    if (!param) {
      url =
        `${configurations('BYBIT_API_HOST')}/v5/market/instruments-info` +
        `?category=linear` +
        `&limit=${configurations('DEFAULT_LIMIT')}`;
    } else {
      url =
        `${configurations('BYBIT_API_HOST')}/v5/market/instruments-info` +
        `?category=linear&symbol=${param.symbol}` +
        `&limit=${configurations('DEFAULT_LIMIT')}`;
    }

    try {
      const response = await axios.get(url, { timeout: 5000 });

      const rawData = response.data?.result?.list;
      if (!Array.isArray(rawData)) {
        this.logger.warn(
          'Unexpected response format from Bybit API',
          logDetail({
            class: 'AppService',
            function: 'fetchBybitInstrument',
            url,
            param,
          }),
        );
        return [];
      }
      const parsedData: Instrument[] = rawData.map((item: any) => ({
        symbol: String(item.symbol ?? ''),

        launchTime: item.launchTime === undefined ? 0 : Number(item.launchTime),
        listedTime: item.listedTime === undefined ? 0 : Number(item.listedTime),

        priceScale: item.priceScale === undefined ? 0 : Number(item.priceScale),

        leverageFilter: {
          minLeverage: Number(item.leverageFilter?.minLeverage ?? 0),
          maxLeverage: Number(item.leverageFilter?.maxLeverage ?? 0),
          leverageStep: Number(item.leverageFilter?.leverageStep ?? 0),
        },

        priceFilter: {
          minPrice: Number(item.priceFilter?.minPrice ?? 0),
          maxPrice: Number(item.priceFilter?.maxPrice ?? 0),
          tickSize: Number(item.priceFilter?.tickSize ?? 0),
        },

        lotSizeFilter: {
          maxOrderQty: Number(item.lotSizeFilter?.maxOrderQty ?? 0),
          minOrderQty: Number(item.lotSizeFilter?.minOrderQty ?? 0),
          qtyStep: Number(item.lotSizeFilter?.qtyStep ?? 0),
          postOnlyMaxOrderQty: Number(
            item.lotSizeFilter?.postOnlyMaxOrderQty ?? 0,
          ),
          maxMktOrderQty: Number(item.lotSizeFilter?.maxMktOrderQty ?? 0),
          minNotionalValue: Number(item.lotSizeFilter?.minNotionalValue ?? 0),
        },

        fundingInterval:
          item.fundingInterval === null || item.fundingInterval === undefined
            ? 0
            : Number(item.fundingInterval),

        upperFundingRate: Number(item.upperFundingRate ?? 0),
        lowerFundingRate: Number(item.lowerFundingRate ?? 0),
        auctionFeeInfo: {
          auctionFeeRate: configurations('BYBIT_AUCTION_FEE_RATE'),
          takerFeeRate: configurations('BYBIT_TAKER_FEE_RATE'),
          makerFeeRate: configurations('BYBIT_MAKER_FEE_RATE'),
        },
      }));

      return parsedData;
    } catch (err) {
      const error = err as AxiosError;

      this.logger.error(
        `Failed to fetch instrument data`,
        logDetail({
          class: 'AppService',
          function: 'fetchBybitInstrument',
          url,
          param,
          error: error.toJSON ? error.toJSON() : error,
        }),
      );

      return await this.fetchBybitInstrument(param);
    }
  }
}
