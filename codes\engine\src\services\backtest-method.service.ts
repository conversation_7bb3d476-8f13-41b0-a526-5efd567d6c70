import { Inject, Injectable } from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';

import { Logger } from 'winston';
import { GetBacktestMethodDto } from 'src/dto/get-backtest-method.dto';
import { Historical } from 'src/interface/historical.interface';
import { Pattern } from 'src/interface/pattern.interface';
import { Instrument } from 'src/interface/instrument.interface';
import { Plan } from 'src/interface/plan.interface';
import { BacktestService } from './backtest.service';
import { PatternService } from './pattern.service';
import { InstrumentService } from './instrument.service';
import { DynamicPlanService } from './dynamic-plan.service';
import { OptimizeStaticPlanService } from './optimize-static-plan.service';
import { logDetail } from 'src/util/log-detail.util';
import { BacktestPerformance } from 'src/interface/backtest-performance.interface';
import configurations from 'src/configurations';
import { MethodResult } from 'src/interface/method-result.interface';
import { StaticPlanService } from './static-plan.service';
import { HistoricalCacheService } from './historical-cache.service';

@Injectable()
export class BacktestMethodService {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    private readonly historicalCacheService: HistoricalCacheService,
    private readonly patternService: PatternService,
    private readonly instrumentService: InstrumentService,
    private readonly dynamicPlanService: DynamicPlanService,
    private readonly backtestService: BacktestService,
    private readonly optimizeStaticPlanService: OptimizeStaticPlanService,
    private readonly staticPlanService: StaticPlanService,
  ) {}

  async getBacktestMethodResult(
    param: GetBacktestMethodDto,
    results?: MethodResult[],
    historical?: Historical[],
    historicalExecution?: Historical[],
    patterns?: Pattern[],
    instrument?: Instrument[],
    plan?: Plan[],
  ): Promise<MethodResult[]> {
    try {
      const historicalData =
        historical ?? (await this.historicalCacheService.getHistorical(param));
      const historicalExecutionData =
        historicalExecution ??
        (await this.historicalCacheService.getHistorical({
          ...param,
          end: new Date(),
          interval: configurations('EXECUTION_INTERVAL'),
        }));
      const patternData =
        patterns ??
        (await this.patternService.getPattern(param, historicalData));
      const instrumentData =
        instrument ??
        (await this.instrumentService.getInstrument({
          symbol: param.symbol,
        }));
      const planData =
        plan ??
        (param.methodType === 'dynamic'
          ? await this.dynamicPlanService.getPlan(
              param,
              historicalData,
              patternData,
              instrumentData,
            )
          : param.enableOptimization
            ? await this.optimizeStaticPlanService.getOptimizedStaticPlan(
                param,
                historicalData,
                historicalExecutionData,
                patternData,
                instrumentData,
              )
            : await this.staticPlanService.getPlan(
                param,
                historicalData,
                patternData,
                instrumentData,
              ));
      const resultsData =
        results ??
        (await this.backtestService.getSingleSymbolResult(
          planData,
          historicalExecutionData,
        ));

      return resultsData;
    } catch (error) {
      this.logger.error(
        'Failed to generate backtest method result',
        logDetail({
          class: 'BacktestMethodService',
          function: 'getBacktestMethodResult',
          param,
          error,
        }),
      );
      throw new Error('Failed to generate backtest method');
    }
  }

  async getBacktestMethodPerformance(
    param: GetBacktestMethodDto,
    results?: MethodResult[],
    historical?: Historical[],
    historicalExecution?: Historical[],
    patterns?: Pattern[],
    instrument?: Instrument[],
    plan?: Plan[],
  ): Promise<BacktestPerformance> {
    try {
      const historicalData =
        historical ?? (await this.historicalCacheService.getHistorical(param));
      const historicalExecutionData =
        historicalExecution ??
        (await this.historicalCacheService.getHistorical({
          ...param,
          end: new Date(),
          interval: configurations('EXECUTION_INTERVAL'),
        }));
      const patternData =
        patterns ??
        (await this.patternService.getPattern(param, historicalData));
      const instrumentData =
        instrument ??
        (await this.instrumentService.getInstrument({
          symbol: param.symbol,
        }));
      const planData =
        plan ??
        (param.methodType === 'dynamic'
          ? await this.dynamicPlanService.getPlan(
              param,
              historicalData,
              patternData,
              instrumentData,
            )
          : param.enableOptimization
            ? await this.optimizeStaticPlanService.getOptimizedStaticPlan(
                param,
                historicalData,
                historicalExecutionData,
                patternData,
                instrumentData,
              )
            : await this.staticPlanService.getPlan(
                param,
                historicalData,
                patternData,
                instrumentData,
              ));
      const resultsData =
        results ??
        (await this.backtestService.getSingleSymbolResult(
          planData,
          historicalExecutionData,
        ));
      const performanceData =
        await this.backtestService.getPerformance(resultsData);

      return performanceData;
    } catch (error) {
      this.logger.error(
        'Failed to generate backtest method performance',
        logDetail({
          class: 'BacktestMethodService',
          function: 'getBacktestMethodPerformance',
          param,
          error,
        }),
      );
      throw new Error('Failed to generate backtest method');
    }
  }

  async getBacktestMethodBoth(
    param: GetBacktestMethodDto,
    historical?: Historical[],
    historicalExecution?: Historical[],
    patterns?: Pattern[],
    instrument?: Instrument[],
    plan?: Plan[],
    results?: MethodResult[],
  ): Promise<{
    result: MethodResult[];
    performance: BacktestPerformance;
  }> {
    try {
      const historicalData =
        historical ?? (await this.historicalCacheService.getHistorical(param));
      const historicalExecutionData =
        historicalExecution ??
        (await this.historicalCacheService.getHistorical({
          ...param,
          end: new Date(),
          interval: configurations('EXECUTION_INTERVAL'),
        }));
      const patternData =
        patterns ??
        (await this.patternService.getPattern(param, historicalData));
      const instrumentData =
        instrument ??
        (await this.instrumentService.getInstrument({
          symbol: param.symbol,
        }));
      const planData =
        plan ??
        (param.methodType === 'dynamic'
          ? await this.dynamicPlanService.getPlan(
              { ...param, methodId: param.methodId ?? '' },
              historicalData,
              patternData,
              instrumentData,
            )
          : param.enableOptimization
            ? await this.optimizeStaticPlanService.getOptimizedStaticPlan(
                { ...param, methodId: param.methodId ?? '' },
                historicalData,
                historicalExecutionData,
                patternData,
                instrumentData,
              )
            : await this.staticPlanService.getPlan(
                { ...param, methodId: param.methodId ?? '' },
                historicalData,
                patternData,
                instrumentData,
              ));
      const resultsData =
        results ??
        (await this.backtestService.getSingleSymbolResult(
          planData,
          historicalExecutionData,
        ));

      const performanceData =
        await this.backtestService.getPerformance(resultsData);
      return { result: resultsData, performance: performanceData };
    } catch (error) {
      this.logger.error(
        'Failed to generate backtest method both',
        logDetail({
          class: 'BacktestMethodService',
          function: 'getBacktestMethodBoth',
          param,
          error,
        }),
      );
      throw new Error('Failed to generate backtest method');
    }
  }
}
