"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HistoricalController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const nest_winston_1 = require("nest-winston");
const winston_1 = require("winston");
const get_historical_dto_1 = require("../dto/get-historical.dto");
const log_detail_util_1 = require("../util/log-detail.util");
const historical_cache_service_1 = require("../services/historical-cache.service");
let HistoricalController = class HistoricalController {
    historicalCacheService;
    logger;
    constructor(historicalCacheService, logger) {
        this.historicalCacheService = historicalCacheService;
        this.logger = logger;
    }
    async get(body) {
        try {
            body.start = new Date(body.start);
            body.end = new Date(body.end);
            const result = await this.historicalCacheService.getHistorical(body);
            return result ?? [];
        }
        catch (error) {
            const message = error?.message || 'Unknown error';
            this.logger.error('Historical data fetch failed', (0, log_detail_util_1.logDetail)({
                class: 'HistoricalController',
                function: 'get',
                body,
                error: error.stack || message,
            }));
            throw new common_1.HttpException(message, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.HistoricalController = HistoricalController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get Historical Data from Database' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of Historical Data',
        isArray: true,
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [get_historical_dto_1.GetHistoricalDto]),
    __metadata("design:returntype", Promise)
], HistoricalController.prototype, "get", null);
exports.HistoricalController = HistoricalController = __decorate([
    (0, swagger_1.ApiTags)('Historical Data'),
    (0, common_1.Controller)('historical'),
    __param(1, (0, common_1.Inject)(nest_winston_1.WINSTON_MODULE_PROVIDER)),
    __metadata("design:paramtypes", [historical_cache_service_1.HistoricalCacheService,
        winston_1.Logger])
], HistoricalController);
//# sourceMappingURL=historical.controller.js.map