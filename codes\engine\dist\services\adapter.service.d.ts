import { Logger } from 'winston';
import { GetHistoricalDto } from 'src/dto/get-historical.dto';
import { GetInstrumentDto } from 'src/dto/get-instrument.dto';
import { Historical } from 'src/interface/historical.interface';
import { Instrument } from 'src/interface/instrument.interface';
export declare class AdapterService {
    private readonly logger;
    constructor(logger: Logger);
    fetchBybitHistorical(param: GetHistoricalDto): Promise<Historical[]>;
    fetchBybitInstrument(param?: GetInstrumentDto): Promise<Instrument[]>;
}
