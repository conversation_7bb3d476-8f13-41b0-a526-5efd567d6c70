"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.isGoodInverseMethod = isGoodInverseMethod;
const configurations_1 = __importDefault(require("../configurations"));
function isGoodInverseMethod(result, performance) {
    const minProbability = 100 - (0, configurations_1.default)('METHOD_MIN_PROBABILITY');
    const maxConsecutiveLoss = (0, configurations_1.default)('METHOD_MAX_CONSECUTIVE_LOSS');
    const minValidTrade = (0, configurations_1.default)('METHOD_MIN_VALID_TRADE');
    const resultLength = result.length > 0;
    return (resultLength &&
        performance.probability <= minProbability &&
        performance.maxConsecutiveProfit <= maxConsecutiveLoss &&
        !performance.totalInvalidTrade &&
        performance.totalValidTrade >= minValidTrade &&
        performance.averageRewardRiskRatio >= 1);
}
//# sourceMappingURL=is-good-inverse-method.util.js.map