"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.calculateMaxStopProfitPercent = calculateMaxStopProfitPercent;
function calculateMaxStopProfitPercent(param, target) {
    const maxStopProfitPercent = param.reduce((acc, cur) => {
        if (target === 'stop' && cur.status === 'loss') {
            acc = Math.min(acc, -Math.abs(cur.stopPercent));
        }
        if (target === 'profit' && cur.status === 'profit') {
            acc = Math.max(acc, Math.abs(cur.profitPercent));
        }
        return acc;
    }, 0);
    return maxStopProfitPercent;
}
//# sourceMappingURL=calculate-max-stop-profit-percent.util.js.map