import { Inject, Injectable } from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { GetHistoricalDto } from 'src/dto/get-historical.dto';
import { Historical } from 'src/interface/historical.interface';
import { logDetail } from 'src/util/log-detail.util';
import { Logger } from 'winston';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class HistoricalCacheService {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  private getFilePath(symbol: string, interval: string): string {
    return path.join('data/historical', symbol, `${interval}.json`);
  }

  private async writeWithRetry(
    filePath: string,
    data: any,
    retries = 3,
  ): Promise<void> {
    const tempFilePath = `${filePath}.tmp`;

    for (let i = 0; i < retries; i++) {
      try {
        // Write to temp file first
        fs.writeFileSync(tempFilePath, JSON.stringify(data, null, 2), 'utf-8');
        // Atomic rename operation
        fs.renameSync(tempFilePath, filePath);
        return;
      } catch (err) {
        if (i === retries - 1) throw err;
        await new Promise((resolve) => setTimeout(resolve, 100 * (i + 1)));
      } finally {
        // Clean up temp file if it exists
        if (fs.existsSync(tempFilePath)) {
          try {
            fs.unlinkSync(tempFilePath);
          } catch (cleanupErr) {
            this.logger.warn(
              'Failed to cleanup temp file',
              logDetail({
                class: 'HistoricalCacheService',
                function: 'writeWithRetry',
                error: cleanupErr,
                param: tempFilePath,
              }),
            );
          }
        }
      }
    }
  }

  private sanitizeJson(raw: string): string {
    return (
      raw
        .trim()
        // Remove trailing commas
        .replace(/,\s*([}\]])/g, '$1')
        // Remove BOM if present
        .replace(/^\uFEFF/, '')
    );
  }

  private readJsonFileSafe(filePath: string): any {
    if (!fs.existsSync(filePath)) return null;

    try {
      const content = fs.readFileSync(filePath, 'utf-8');
      const sanitized = this.sanitizeJson(content);
      return sanitized ? JSON.parse(sanitized) : null;
    } catch (err) {
      return null;
    }
  }

  async validateCache(symbol: string, interval: string): Promise<boolean> {
    const filePath = this.getFilePath(symbol, interval);
    if (!fs.existsSync(filePath)) return true;

    try {
      const content = this.readJsonFileSafe(filePath);
      if (content === null || !Array.isArray(content)) {
        await this.writeWithRetry(filePath, []);
        return false;
      }
      return true;
    } catch (err) {
      this.logger.warn(`Repairing corrupted cache file ${filePath}`);
      await this.writeWithRetry(filePath, []);
      return false;
    }
  }

  async getHistorical(param: GetHistoricalDto): Promise<Historical[]> {
    const { symbol, interval, start, end, limit, sort } = param;
    if (!symbol || !interval) return [];

    const filePath = this.getFilePath(symbol, interval);
    if (!fs.existsSync(filePath)) return [];

    const stats = fs.statSync(filePath);
    if (stats.size === 0) {
      this.logger.warn(`Empty historical cache file for ${symbol}-${interval}`);
      return [];
    }

    try {
      const content = this.readJsonFileSafe(filePath);
      if (!content || !Array.isArray(content)) {
        this.logger.warn(
          `Expected array in historical cache file but got ${typeof content}`,
          logDetail({ param: { symbol, interval, filePath } }),
        );
        return [];
      }

      // Filter and normalize
      let data = content
        .filter((d: any) => d?.date && d?.symbol && d?.interval)
        .map((d: any) => {
          const parsedDate = new Date(d.date);
          return {
            ...d,
            date: isNaN(parsedDate.getTime()) ? new Date(0) : parsedDate,
          };
        });

      // Filter by start/end
      if (start) {
        data = data.filter((d) => d.date >= start);
      }
      if (end) {
        data = data.filter((d) => d.date <= end);
      }

      // Sort
      data.sort((a, b) =>
        sort === 'DESC'
          ? b.date.getTime() - a.date.getTime()
          : a.date.getTime() - b.date.getTime(),
      );

      // Limit
      if (limit) {
        data = data.slice(0, limit);
      }

      return data;
    } catch (err) {
      await this.validateCache(symbol, interval);
      this.logger.error(
        'Failed to read historical data',
        logDetail({
          class: 'HistoricalCacheService',
          function: 'getHistorical',
          error: err,
          param,
        }),
      );
      return [];
    }
  }

  async insertHistorical(param: Historical[]): Promise<void> {
    if (!param.length) return;

    const symbol = param[0].symbol;
    const interval = param[0].interval;
    const dirPath = path.join('data/historical', symbol);
    const filePath = this.getFilePath(symbol, interval);

    fs.mkdirSync(dirPath, { recursive: true });

    // Create backup if file is corrupted
    if (fs.existsSync(filePath)) {
      try {
        const content = this.readJsonFileSafe(filePath);
        if (content === null) {
          const backupPath = `${filePath}.corrupted.${Date.now()}`;
          fs.copyFileSync(filePath, backupPath);
          this.logger.warn(`Backed up corrupted file to ${backupPath}`);
        }
      } catch (err) {
        const backupPath = `${filePath}.corrupted.${Date.now()}`;
        fs.copyFileSync(filePath, backupPath);
        this.logger.warn(`Backed up corrupted file to ${backupPath}`);
      }
    }

    let existingData: Historical[] = [];
    if (fs.existsSync(filePath)) {
      const content = this.readJsonFileSafe(filePath);
      if (content && Array.isArray(content)) {
        existingData = content;
      }
    }

    // Merge and deduplicate
    const map = new Map<string, Historical>();
    [...existingData, ...param].forEach((item) => {
      map.set(new Date(item.date).toISOString(), item);
    });

    const merged = Array.from(map.values()).sort(
      (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime(),
    );

    await this.writeWithRetry(filePath, merged);
  }

  async deleteHistorical(param: {
    symbol: string;
    interval: string;
  }): Promise<void> {
    const filePath = this.getFilePath(param.symbol, param.interval);
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
  }

  async countHistorical(
    symbol: string,
    interval: string,
    start?: Date,
    end?: Date,
  ): Promise<number> {
    const filePath = this.getFilePath(symbol, interval);
    if (!fs.existsSync(filePath)) return 0;

    try {
      let content = this.readJsonFileSafe(filePath).map((d: any) => {
        const parsedDate = new Date(d.date);
        return {
          ...d,
          date: isNaN(parsedDate.getTime()) ? new Date(0) : parsedDate,
        };
      });
      if (!content || !Array.isArray(content)) return 0;

      if (start) {
        content = content.filter((d: any) => d.date >= start);
      }
      if (end) {
        content = content.filter((d: any) => d.date <= end);
      }
      const count = content.length;
      return count;
    } catch (err) {
      await this.validateCache(symbol, interval);
      this.logger.error(
        'Failed to count historical cache',
        logDetail({
          class: 'HistoricalCacheService',
          function: 'countHistorical',
          error: err,
          param: { symbol, interval, start, end },
        }),
      );
      return 0;
    }
  }

  async cleanupOldBackups(maxAgeDays = 7): Promise<void> {
    const now = Date.now();
    const maxAgeMs = maxAgeDays * 24 * 60 * 60 * 1000;

    if (!fs.existsSync('data/historical')) return;

    const symbolDirs = fs.readdirSync('data/historical');
    for (const symbolDir of symbolDirs) {
      const symbolPath = path.join('data/historical', symbolDir);
      const files = fs.readdirSync(symbolPath);

      for (const file of files) {
        if (file.includes('.corrupted.')) {
          const filePath = path.join(symbolPath, file);
          try {
            const stats = fs.statSync(filePath);
            if (now - stats.mtimeMs > maxAgeMs) {
              fs.unlinkSync(filePath);
              this.logger.debug(`Cleaned up old backup: ${filePath}`);
            }
          } catch (err) {
            this.logger.warn(
              `Failed to cleanup backup ${filePath}`,
              logDetail({ error: err }),
            );
          }
        }
      }
    }
  }
}
